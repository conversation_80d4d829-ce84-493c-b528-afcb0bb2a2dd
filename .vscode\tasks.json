{"version": "2.0.0", "tasks": [{"type": "docker-build", "label": "docker-build", "platform": "python", "dockerBuild": {"tag": "1020120:latest", "dockerfile": "${workspaceFolder}/Dockerfile", "context": "${workspaceFolder}", "pull": true}}, {"type": "docker-run", "label": "docker-run: debug", "dependsOn": ["docker-build"], "python": {"args": ["test_import:app", "--host", "0.0.0.0", "--port", "8000"], "module": "u<PERSON><PERSON>"}}]}