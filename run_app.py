#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام المحاسبة
"""

import sys
import os

def main():
    """تشغيل التطبيق الرئيسي"""
    try:
        print("🚀 بدء تشغيل نظام المحاسبة...")
        
        # التحقق من المكتبات المطلوبة
        required_modules = [
            'customtkinter',
            'pymongo', 
            'bcrypt',
            'tkinter'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module} متوفر")
            except ImportError:
                missing_modules.append(module)
                print(f"❌ {module} غير متوفر")
        
        if missing_modules:
            print(f"\n⚠️ المكتبات التالية مفقودة: {', '.join(missing_modules)}")
            print("يرجى تثبيتها باستخدام: pip install " + " ".join(missing_modules))
            return False
        
        # تشغيل التطبيق
        print("\n📱 تشغيل واجهة التطبيق...")
        import accounting_app
        
        if __name__ == "__main__":
            app = accounting_app.AccountingApp()
            app.run()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
