@echo off
chcp 65001 >nul
echo ====================================
echo 🏢 نظام المحاسبة المطور
echo ====================================
echo.
echo 🚀 بدء تشغيل التطبيق...
echo.

python accounting_app.py

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo 💡 تأكد من تثبيت جميع المكتبات المطلوبة
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح
)

pause
