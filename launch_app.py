#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام المحاسبة مع معالجة شاملة للأخطاء
"""

import sys
import traceback
import os
from pathlib import Path

def check_environment():
    """فحص البيئة والمتطلبات"""
    print("🔍 فحص البيئة...")
    
    # فحص Python
    print(f"🐍 Python: {sys.version}")
    
    # فحص المجلد الحالي
    current_dir = Path.cwd()
    print(f"📁 المجلد الحالي: {current_dir}")
    
    # فحص وجود الملف
    app_file = current_dir / "accounting_app.py"
    if not app_file.exists():
        print(f"❌ ملف accounting_app.py غير موجود في {current_dir}")
        return False
    
    print(f"✅ ملف accounting_app.py موجود ({app_file.stat().st_size:,} بايت)")
    
    # فحص المكتبات
    required_modules = [
        'customtkinter',
        'pymongo',
        'bcrypt',
        'tkinter',
        'datetime',
        'threading',
        'pathlib'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module}")
    
    if missing_modules:
        print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing_modules)}")
        return False
    
    return True

def run_app():
    """تشغيل التطبيق"""
    try:
        print("\n🚀 بدء تشغيل نظام المحاسبة...")
        
        # استيراد التطبيق
        print("📦 استيراد التطبيق...")
        sys.path.insert(0, str(Path.cwd()))
        
        import accounting_app
        print("✅ تم استيراد accounting_app")
        
        # إنشاء التطبيق
        print("🏗️ إنشاء كائن التطبيق...")
        app = accounting_app.AccountingApp()
        print("✅ تم إنشاء AccountingApp")
        
        # تشغيل التطبيق
        print("🎯 تشغيل واجهة المستخدم...")
        print("📱 سيتم فتح نافذة تسجيل الدخول...")
        
        app.run()
        
        print("✅ تم إغلاق التطبيق بنجاح")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت جميع المكتبات المطلوبة")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print(f"\n📋 تفاصيل الخطأ:")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🏢 نظام المحاسبة المطور")
    print("=" * 50)
    
    # فحص البيئة
    if not check_environment():
        print("\n❌ فشل في فحص البيئة")
        input("اضغط Enter للخروج...")
        return
    
    print("\n✅ البيئة جاهزة للتشغيل")
    
    # تشغيل التطبيق
    success = run_app()
    
    if not success:
        print("\n❌ فشل في تشغيل التطبيق")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
