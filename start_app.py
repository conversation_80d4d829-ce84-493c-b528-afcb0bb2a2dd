#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام المحاسبة المبسط
"""

import sys
import traceback

def main():
    """تشغيل التطبيق"""
    try:
        print("🚀 بدء تشغيل نظام المحاسبة...")
        
        # استيراد التطبيق
        import accounting_app
        print("✅ تم استيراد التطبيق بنجاح")
        
        # إنشاء كائن التطبيق
        app = accounting_app.AccountingApp()
        print("✅ تم إنشاء كائن التطبيق")
        
        # تشغيل التطبيق
        print("📱 تشغيل واجهة المستخدم...")
        app.run()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("\n📋 تفاصيل الخطأ:")
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
