#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع للتطبيق المحاسبي
يتحقق من المتطلبات ويشغل التطبيق
"""

import sys
import subprocess
import importlib
import os
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_mongodb():
    """التحقق من MongoDB"""
    try:
        import pymongo
        client = pymongo.MongoClient('mongodb://localhost:27017/', serverSelectionTimeoutMS=3000)
        client.server_info()
        print("✅ MongoDB متصل ويعمل")
        client.close()
        return True
    except ImportError:
        print("❌ مكتبة pymongo غير مثبتة")
        return False
    except Exception as e:
        print(f"❌ MongoDB غير متصل: {e}")
        print("تأكد من تشغيل MongoDB على المنفذ 27017")
        return False

def check_required_packages():
    """التحقق من المكتبات المطلوبة"""
    required_packages = [
        'customtkinter',
        'pymongo',
        'bcrypt',
        'PIL',  # Pillow
        'reportlab',
        'openpyxl',
        'pandas',
        'matplotlib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} غير مثبت")
            missing_packages.append(package)
    
    return missing_packages

def install_requirements():
    """تثبيت المتطلبات"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        print("🔄 جاري تثبيت المتطلبات...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت المتطلبات بنجاح")
            return True
        else:
            print(f"❌ فشل في تثبيت المتطلبات: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def run_application():
    """تشغيل التطبيق"""
    app_file = Path("accounting_app.py")
    
    if not app_file.exists():
        print("❌ ملف التطبيق accounting_app.py غير موجود")
        return False
    
    try:
        print("🚀 جاري تشغيل التطبيق...")
        print("=" * 50)
        
        # تشغيل التطبيق
        subprocess.run([sys.executable, "accounting_app.py"])
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🏢 نظام المحاسبة المتكامل")
    print("   تطبيق محاسبي شامل")
    print("=" * 50)
    print()
    
    # التحقق من إصدار Python
    print("🔍 التحقق من المتطلبات...")
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من المكتبات
    missing_packages = check_required_packages()
    
    if missing_packages:
        print(f"\n📦 يجب تثبيت {len(missing_packages)} مكتبة مفقودة")
        response = input("هل تريد تثبيتها الآن؟ (y/n): ").lower().strip()
        
        if response in ['y', 'yes', 'نعم', 'ن']:
            if not install_requirements():
                input("اضغط Enter للخروج...")
                return
        else:
            print("❌ لا يمكن تشغيل التطبيق بدون المكتبات المطلوبة")
            input("اضغط Enter للخروج...")
            return
    
    # التحقق من MongoDB
    print("\n🔍 التحقق من قاعدة البيانات...")
    if not check_mongodb():
        print("\n💡 تعليمات تشغيل MongoDB:")
        print("   Windows: net start MongoDB")
        print("   macOS: brew services start mongodb/brew/mongodb-community")
        print("   Linux: sudo systemctl start mongod")
        print("\n📖 راجع ملف setup_mongodb.md للتفاصيل")
        
        response = input("\nهل تريد المتابعة رغم ذلك؟ (y/n): ").lower().strip()
        if response not in ['y', 'yes', 'نعم', 'ن']:
            return
    
    # تشغيل التطبيق
    print("\n" + "=" * 50)
    run_application()
    
    print("\n" + "=" * 50)
    print("شكراً لاستخدام نظام المحاسبة المتكامل!")
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()