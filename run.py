#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🚀 بدء تشغيل نظام المحاسبة...")
    
    # استيراد التطبيق
    import accounting_app
    
    # تشغيل التطبيق
    if __name__ == "__main__":
        app = accounting_app.AccountingApp()
        app.run()
        
except Exception as e:
    print(f"خطأ: {e}")
    import traceback
    traceback.print_exc()
    input("اضغط Enter للخروج...")
