#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتطبيق المحاسبي
"""

import customtkinter as ctk
from tkinter import messagebox
import json
import os
from datetime import datetime

# إعدادات التطبيق
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

class TestApp:
    def __init__(self):
        """تهيئة التطبيق"""
        self.root = ctk.CTk()
        self.root.title("نظام المحاسبة - اختبار")
        self.root.geometry("800x600")
        
        self.center_window()
        self.create_widgets()
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.root,
            text="🏢 نظام المحاسبة الذكي",
            font=ctk.CTkFont(size=32, weight="bold")
        )
        title_label.pack(pady=(50, 30))
        
        # وصف التطبيق
        description_label = ctk.CTkLabel(
            self.root,
            text="نظام محاسبي شامل لإدارة الحسابات والمعاملات المالية",
            font=ctk.CTkFont(size=16)
        )
        description_label.pack(pady=(0, 50))
        
        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(self.root)
        buttons_frame.pack(pady=20)
        
        # أزرار الأدوار
        admin_button = ctk.CTkButton(
            buttons_frame,
            text="👨‍💼 واجهة المدير",
            command=lambda: self.show_interface("admin"),
            width=200,
            height=50,
            font=ctk.CTkFont(size=16)
        )
        admin_button.pack(pady=10, padx=20)
        
        accountant_button = ctk.CTkButton(
            buttons_frame,
            text="📊 واجهة المحاسب",
            command=lambda: self.show_interface("accountant"),
            width=200,
            height=50,
            font=ctk.CTkFont(size=16)
        )
        accountant_button.pack(pady=10, padx=20)
        
        supplier_button = ctk.CTkButton(
            buttons_frame,
            text="🚚 واجهة المورد",
            command=lambda: self.show_interface("supplier"),
            width=200,
            height=50,
            font=ctk.CTkFont(size=16)
        )
        supplier_button.pack(pady=10, padx=20)
        
        # معلومات التطبيق
        info_frame = ctk.CTkFrame(self.root)
        info_frame.pack(fill="x", padx=20, pady=20)
        
        info_text = """
✅ تم تطوير جميع الواجهات بنجاح
✅ واجهة المدير: إدارة المستخدمين والنظام
✅ واجهة المحاسب: إدارة المعاملات والتقارير
✅ واجهة المورد: إدارة الفواتير وطلبات التوريد

📋 المتطلبات:
• Python 3.8+
• CustomTkinter
• MongoDB (للإصدار الكامل)
• PyMongo, bcrypt, reportlab

🚀 حالة التطوير: مكتمل
        """
        
        info_label = ctk.CTkLabel(
            info_frame,
            text=info_text,
            font=ctk.CTkFont(size=12),
            justify="right"
        )
        info_label.pack(pady=20, padx=20)
        
        # زر الخروج
        exit_button = ctk.CTkButton(
            self.root,
            text="خروج",
            command=self.root.quit,
            width=100,
            height=35,
            fg_color="red",
            hover_color="darkred"
        )
        exit_button.pack(pady=20)
    
    def show_interface(self, interface_type):
        """عرض واجهة معينة"""
        interfaces = {
            "admin": "👨‍💼 واجهة المدير",
            "accountant": "📊 واجهة المحاسب", 
            "supplier": "🚚 واجهة المورد"
        }
        
        features = {
            "admin": [
                "• إدارة المستخدمين والصلاحيات",
                "• مراقبة النظام والأمان",
                "• إعدادات التطبيق العامة",
                "• تقارير شاملة للنظام",
                "• إدارة النسخ الاحتياطية"
            ],
            "accountant": [
                "• إدارة المعاملات المالية",
                "• إنشاء وإدارة الحسابات",
                "• إنتاج التقارير المحاسبية",
                "• إدارة القيود اليومية",
                "• متابعة المدفوعات"
            ],
            "supplier": [
                "• إدارة الفواتير والمستحقات",
                "• تقديم طلبات التوريد",
                "• متابعة حالة المدفوعات",
                "• إدارة المستندات",
                "• التواصل مع الإدارة"
            ]
        }
        
        interface_name = interfaces.get(interface_type, "غير معروف")
        interface_features = "\n".join(features.get(interface_type, []))
        
        message = f"""
{interface_name}

الميزات المتاحة:
{interface_features}

✅ تم تطوير هذه الواجهة بالكامل في التطبيق الرئيسي
📁 الملف: accounting_app.py

لتشغيل التطبيق الكامل:
1. تأكد من تثبيت MongoDB
2. شغل الأمر: python accounting_app.py
        """
        
        messagebox.showinfo(interface_name, message)
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = TestApp()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()
