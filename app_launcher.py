#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل نظام المحاسبة مع تشخيص شامل
"""

import sys
import os
import traceback
from pathlib import Path

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    
    print("=" * 60)
    print("🏢 نظام المحاسبة المطور - مشغل التطبيق")
    print("=" * 60)
    
    try:
        # التحقق من البيئة
        print(f"🐍 إصدار Python: {sys.version}")
        print(f"📁 المجلد الحالي: {Path.cwd()}")
        
        # التحقق من وجود الملف الرئيسي
        app_file = Path("accounting_app.py")
        if not app_file.exists():
            print(f"❌ الملف accounting_app.py غير موجود!")
            return False
            
        print(f"✅ الملف الرئيسي موجود ({app_file.stat().st_size:,} بايت)")
        
        # التحقق من المكتبات الأساسية
        print("\n📚 فحص المكتبات:")
        
        try:
            import customtkinter
            print(f"✅ CustomTkinter: {customtkinter.__version__}")
        except ImportError as e:
            print(f"❌ CustomTkinter: {e}")
            return False
            
        try:
            import pymongo
            print(f"✅ PyMongo: {pymongo.__version__}")
        except ImportError as e:
            print(f"❌ PyMongo: {e}")
            return False
            
        try:
            import bcrypt
            print(f"✅ bcrypt: {bcrypt.__version__}")
        except ImportError as e:
            print(f"❌ bcrypt: {e}")
            return False
            
        try:
            import tkinter
            print(f"✅ Tkinter: متوفر")
        except ImportError as e:
            print(f"❌ Tkinter: {e}")
            return False
        
        # استيراد التطبيق
        print(f"\n📦 استيراد التطبيق...")
        try:
            import accounting_app
            print(f"✅ تم استيراد accounting_app بنجاح")
        except Exception as e:
            print(f"❌ فشل في استيراد accounting_app: {e}")
            traceback.print_exc()
            return False
        
        # إنشاء كائن التطبيق
        print(f"\n🏗️ إنشاء كائن التطبيق...")
        try:
            app = accounting_app.AccountingApp()
            print(f"✅ تم إنشاء AccountingApp بنجاح")
        except Exception as e:
            print(f"❌ فشل في إنشاء AccountingApp: {e}")
            traceback.print_exc()
            return False
        
        # تشغيل التطبيق
        print(f"\n🚀 تشغيل التطبيق...")
        print(f"📱 سيتم فتح نافذة تسجيل الدخول...")
        print(f"🔑 بيانات الدخول الافتراضية:")
        print(f"   المدير: admin / admin123")
        print(f"   المحاسب: accountant / acc123")
        print(f"   المورد: supplier / sup123")
        print(f"\n" + "=" * 60)
        
        try:
            app.run()
            print(f"\n✅ تم إغلاق التطبيق بنجاح")
            return True
        except Exception as e:
            print(f"❌ خطأ أثناء تشغيل التطبيق: {e}")
            traceback.print_exc()
            return False
            
    except KeyboardInterrupt:
        print(f"\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if not success:
        print(f"\n❌ فشل في تشغيل التطبيق")
        print(f"💡 تأكد من:")
        print(f"   - تثبيت جميع المكتبات المطلوبة")
        print(f"   - وجود ملف accounting_app.py")
        print(f"   - عدم وجود أخطاء في الكود")
        
    input(f"\nاضغط Enter للخروج...")
