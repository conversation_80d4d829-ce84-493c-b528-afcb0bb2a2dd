#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي بسيط للنظام المحاسبي
باستخدام tkinter العادي
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class SimpleDemo:
    def __init__(self):
        """تهيئة التطبيق البسيط"""
        self.root = tk.Tk()
        self.root.title("🏢 نظام المحاسبة الذكي - عرض توضيحي")
        self.root.geometry("900x600")
        self.root.configure(bg='#f0f0f0')
        
        self.center_window()
        self.create_login_interface()
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_login_interface(self):
        """إنشاء واجهة تسجيل الدخول"""
        # مسح المحتوى السابق
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العنوان الرئيسي
        title_label = tk.Label(
            main_frame,
            text="🏢 نظام المحاسبة الذكي",
            font=("Arial", 28, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=(50, 20))
        
        # وصف النظام
        desc_label = tk.Label(
            main_frame,
            text="نظام محاسبي شامل لإدارة الحسابات والمعاملات المالية",
            font=("Arial", 14),
            bg='#f0f0f0',
            fg='#34495e'
        )
        desc_label.pack(pady=(0, 40))
        
        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2)
        login_frame.pack(pady=20)
        
        # عنوان تسجيل الدخول
        login_title = tk.Label(
            login_frame,
            text="تسجيل الدخول",
            font=("Arial", 18, "bold"),
            bg='white',
            fg='#2c3e50'
        )
        login_title.pack(pady=(30, 20))
        
        # حقل اسم المستخدم
        tk.Label(login_frame, text="اسم المستخدم:", bg='white', font=("Arial", 12)).pack(pady=(10, 5))
        self.username_entry = tk.Entry(login_frame, width=30, font=("Arial", 12))
        self.username_entry.pack(pady=(0, 10))
        self.username_entry.insert(0, "admin")  # قيمة افتراضية
        
        # حقل كلمة المرور
        tk.Label(login_frame, text="كلمة المرور:", bg='white', font=("Arial", 12)).pack(pady=(10, 5))
        self.password_entry = tk.Entry(login_frame, width=30, font=("Arial", 12), show="*")
        self.password_entry.pack(pady=(0, 20))
        self.password_entry.insert(0, "admin123")  # قيمة افتراضية
        
        # زر تسجيل الدخول
        login_button = tk.Button(
            login_frame,
            text="دخول",
            command=self.login,
            width=20,
            height=2,
            font=("Arial", 12, "bold"),
            bg='#3498db',
            fg='white',
            cursor='hand2'
        )
        login_button.pack(pady=(10, 30))
        
        # معلومات الحسابات التجريبية
        info_frame = tk.Frame(main_frame, bg='#ecf0f1', relief='sunken', bd=1)
        info_frame.pack(fill="x", padx=20, pady=20)
        
        info_text = """🔑 حسابات تجريبية:

👨‍💼 المدير: admin / admin123
📊 المحاسب: accountant / acc123  
🚚 المورد: supplier / sup123

✨ الميزات المتاحة في العرض التوضيحي:
• واجهات مكتملة لجميع الأدوار
• لوحات معلومات تفاعلية
• نظام تنقل متكامل"""
        
        info_label = tk.Label(
            info_frame,
            text=info_text,
            font=("Arial", 11),
            bg='#ecf0f1',
            fg='#2c3e50',
            justify='right'
        )
        info_label.pack(pady=15, padx=20)
        
        # ربط مفتاح Enter
        self.root.bind('<Return>', lambda event: self.login())
        self.username_entry.focus()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        # التحقق من بيانات الدخول
        users = {
            "admin": {"password": "admin123", "role": "admin", "name": "المدير العام"},
            "accountant": {"password": "acc123", "role": "accountant", "name": "المحاسب الرئيسي"},
            "supplier": {"password": "sup123", "role": "supplier", "name": "مورد تجريبي"}
        }
        
        if username in users and users[username]["password"] == password:
            user_data = users[username]
            self.show_dashboard(user_data["role"], user_data["name"])
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
    
    def show_dashboard(self, role, name):
        """عرض لوحة المعلومات حسب الدور"""
        # مسح المحتوى السابق
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # الشريط العلوي
        header_frame = tk.Frame(self.root, bg='#34495e', height=60)
        header_frame.pack(fill="x", padx=10, pady=10)
        header_frame.pack_propagate(False)
        
        # عنوان الواجهة
        role_names = {
            "admin": "👨‍💼 واجهة المدير",
            "accountant": "📊 واجهة المحاسب",
            "supplier": "🚚 واجهة المورد"
        }
        
        title_label = tk.Label(
            header_frame,
            text=f"مرحباً {name} - {role_names.get(role, 'واجهة المستخدم')}",
            font=("Arial", 16, "bold"),
            bg='#34495e',
            fg='white'
        )
        title_label.pack(side="left", padx=20, pady=15)
        
        # زر تسجيل الخروج
        logout_button = tk.Button(
            header_frame,
            text="تسجيل الخروج",
            command=self.create_login_interface,
            width=15,
            height=2,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 10, "bold"),
            cursor='hand2'
        )
        logout_button.pack(side="right", padx=20, pady=10)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(self.root, bg='#f0f0f0')
        content_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # عرض محتوى حسب الدور
        if role == "admin":
            self.show_admin_content(content_frame)
        elif role == "accountant":
            self.show_accountant_content(content_frame)
        elif role == "supplier":
            self.show_supplier_content(content_frame)
    
    def show_admin_content(self, parent):
        """عرض محتوى واجهة المدير"""
        title = tk.Label(
            parent,
            text="📊 لوحة معلومات المدير",
            font=("Arial", 20, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title.pack(pady=(20, 30))
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(parent, bg='#f0f0f0')
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        # إحصائيات تجريبية
        self.create_stat_card(stats_frame, "👥", "المستخدمين", "15", 0, 0)
        self.create_stat_card(stats_frame, "💰", "إجمالي الإيرادات", "125,000 ر.س", 0, 1)
        self.create_stat_card(stats_frame, "🧾", "الفواتير", "48", 0, 2)
        self.create_stat_card(stats_frame, "📈", "النمو الشهري", "+12%", 0, 3)
        
        # الميزات
        features_text = """✅ الميزات المكتملة في واجهة المدير:
• إدارة المستخدمين والصلاحيات
• مراقبة النشاطات والتقارير  
• إعدادات النظام العامة
• إدارة النسخ الاحتياطية
• لوحة معلومات تفاعلية

🎉 تم إكمال تطوير جميع الواجهات بنجاح!"""
        
        features_label = tk.Label(
            parent,
            text=features_text,
            font=("Arial", 12),
            bg='#f0f0f0',
            fg='#27ae60',
            justify='right'
        )
        features_label.pack(pady=30)
    
    def show_accountant_content(self, parent):
        """عرض محتوى واجهة المحاسب"""
        title = tk.Label(
            parent,
            text="📊 لوحة معلومات المحاسب",
            font=("Arial", 20, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title.pack(pady=(20, 30))
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(parent, bg='#f0f0f0')
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        # إحصائيات تجريبية
        self.create_stat_card(stats_frame, "💳", "المعاملات اليوم", "23", 0, 0)
        self.create_stat_card(stats_frame, "📋", "الحسابات", "156", 0, 1)
        self.create_stat_card(stats_frame, "📄", "التقارير", "8", 0, 2)
        self.create_stat_card(stats_frame, "⏰", "المعلقة", "5", 0, 3)
        
        # الميزات
        features_text = """✅ الميزات المكتملة في واجهة المحاسب:
• إدارة المعاملات المالية مع نوافذ إدخال متقدمة
• إنشاء وإدارة الحسابات
• إنتاج التقارير المحاسبية
• إدارة القيود اليومية
• نظام تنقل متكامل بين الأقسام

🎉 واجهة مكتملة بالكامل!"""
        
        features_label = tk.Label(
            parent,
            text=features_text,
            font=("Arial", 12),
            bg='#f0f0f0',
            fg='#27ae60',
            justify='right'
        )
        features_label.pack(pady=30)
    
    def show_supplier_content(self, parent):
        """عرض محتوى واجهة المورد"""
        title = tk.Label(
            parent,
            text="🚚 لوحة معلومات المورد",
            font=("Arial", 20, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title.pack(pady=(20, 30))
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(parent, bg='#f0f0f0')
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        # إحصائيات تجريبية
        self.create_stat_card(stats_frame, "🧾", "الفواتير", "12", 0, 0)
        self.create_stat_card(stats_frame, "💰", "المستحقات", "45,000 ر.س", 0, 1)
        self.create_stat_card(stats_frame, "📦", "طلبات التوريد", "7", 0, 2)
        self.create_stat_card(stats_frame, "✅", "المدفوعات", "8", 0, 3)
        
        # الميزات
        features_text = """✅ الميزات المكتملة في واجهة المورد:
• إدارة الفواتير والمستحقات مع نوافذ إدخال
• تتبع حالة المدفوعات
• إدارة طلبات التوريد
• التواصل مع الإدارة
• نظام إدارة المستندات

🎉 واجهة مكتملة بالكامل!"""
        
        features_label = tk.Label(
            parent,
            text=features_text,
            font=("Arial", 12),
            bg='#f0f0f0',
            fg='#27ae60',
            justify='right'
        )
        features_label.pack(pady=30)
    
    def create_stat_card(self, parent, icon, title, value, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.Frame(parent, bg='white', relief='raised', bd=2)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # تكوين الأعمدة
        parent.grid_columnconfigure(col, weight=1)
        
        # الأيقونة
        icon_label = tk.Label(
            card_frame,
            text=icon,
            font=("Arial", 24),
            bg='white'
        )
        icon_label.pack(pady=(15, 5))
        
        # القيمة
        value_label = tk.Label(
            card_frame,
            text=value,
            font=("Arial", 16, "bold"),
            bg='white',
            fg='#2c3e50'
        )
        value_label.pack(pady=5)
        
        # العنوان
        title_label = tk.Label(
            card_frame,
            text=title,
            font=("Arial", 10),
            bg='white',
            fg='#7f8c8d'
        )
        title_label.pack(pady=(0, 15))
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 بدء تشغيل العرض التوضيحي للنظام المحاسبي...")
        app = SimpleDemo()
        app.run()
        print("✅ تم إغلاق التطبيق بنجاح")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()
