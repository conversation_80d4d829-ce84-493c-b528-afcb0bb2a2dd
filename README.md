# 🏢 نظام المحاسبة المتكامل

تطبيق سطح مكتب محاسبي شامل مطور بلغة Python مع قاعدة بيانات MongoDB

## ✅ حالة المشروع: مكتمل بالكامل

تم إكمال تطوير جميع واجهات النظام وميزاته بنجاح!

## الميزات الرئيسية

### 🔐 نظام المصادقة والأمان ✅
- تسجيل دخول آمن مع تشفير كلمات المرور
- ثلاثة أنواع من المستخدمين: مدير، محاسب، مورد
- جلسات آمنة مع انتهاء صلاحية تلقائي

### 👨‍💼 واجهة المدير ✅ مكتملة
- لوحة معلومات شاملة مع إحصائيات سريعة
- إدارة المستخدمين (إضافة/تعديل/حذف)
- إدارة الموردين والمحاسبين
- تقارير مالية متنوعة
- إعدادات النظام
- نظام الإشعارات

### 🧾 واجهة المحاسب ✅ مكتملة
- تسجيل المعاملات اليومية مع نوافذ إدخال متقدمة
- إدارة الحسابات والقيود
- توليد التقارير المحاسبية
- إرسال الإشعارات للإدارة
- لوحة معلومات تفاعلية مع بطاقات إحصائية
- نظام تنقل متكامل بين الأقسام

### 🚚 واجهة المورد ✅ مكتملة
- عرض الفواتير والمستحقات مع إمكانية الإضافة
- تقديم طلبات توريد جديدة
- تتبع المدفوعات والأرصدة
- رفع المستندات والفواتير
- لوحة معلومات تفاعلية
- نظام إدارة شامل للموردين

## المتطلبات

### متطلبات النظام
- Python 3.8 أو أحدث
- MongoDB 4.4 أو أحدث
- نظام التشغيل: Windows 10/11, macOS, Linux

### المكتبات المطلوبة
```
pymongo==4.6.0
customtkinter==5.2.0
Pillow==10.1.0
reportlab==4.0.7
openpyxl==3.1.2
pandas==2.1.4
matplotlib==3.8.2
bcrypt==4.1.2
python-dateutil==2.8.2
tkcalendar==1.6.1
plyer==2.1.0
```

## التثبيت والتشغيل

### 1. تثبيت MongoDB
قم بتحميل وتثبيت MongoDB من الموقع الرسمي:
https://www.mongodb.com/try/download/community

### 2. تثبيت Python
تأكد من تثبيت Python 3.8 أو أحدث من:
https://www.python.org/downloads/

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. تشغيل MongoDB
تأكد من تشغيل خدمة MongoDB على المنفذ الافتراضي 27017

### 5. تشغيل التطبيق
```bash
python accounting_app.py
```

## بيانات تسجيل الدخول الافتراضية

### المدير الافتراضي
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## هيكل قاعدة البيانات

### المجموعات (Collections)
- `users` - بيانات المستخدمين
- `transactions` - المعاملات المالية
- `accounts` - الحسابات المحاسبية
- `suppliers` - بيانات الموردين
- `invoices` - الفواتير
- `payments` - المدفوعات
- `notifications` - الإشعارات
- `reports` - التقارير المحفوظة

## الميزات المتقدمة

### 📊 التقارير
- تقارير المعاملات الشهرية
- تقارير الموردين
- تقارير الفواتير والمدفوعات
- تصدير التقارير إلى PDF وExcel

### 🔔 نظام الإشعارات
- إشعارات سطح المكتب
- إشعارات داخل التطبيق
- تنبيهات الفواتير المستحقة

### 💾 النسخ الاحتياطي
- نسخ احتياطي تلقائي
- استعادة البيانات
- تصدير واستيراد البيانات

### 🔒 الأمان
- تشفير كلمات المرور باستخدام bcrypt
- جلسات آمنة
- تسجيل العمليات

## الاستخدام

### للمدير
1. تسجيل الدخول باستخدام بيانات المدير
2. إضافة المستخدمين (محاسبين وموردين)
3. مراقبة الأنشطة من لوحة المعلومات
4. إنشاء التقارير المالية
5. إدارة إعدادات النظام

### للمحاسب
1. تسجيل الدخول باستخدام بيانات المحاسب
2. تسجيل المعاملات اليومية
3. إدارة الحسابات
4. إنشاء التقارير
5. التواصل مع الإدارة

### للمورد
1. تسجيل الدخول باستخدام بيانات المورد
2. عرض الفواتير والمستحقات
3. تقديم طلبات التوريد
4. تتبع المدفوعات
5. رفع المستندات

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXX-XXXX

## الترخيص

هذا التطبيق مطور لأغراض تعليمية وتجارية.
جميع الحقوق محفوظة © 2024

## التطوير المستقبلي

### الميزات المخطط إضافتها
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تطبيق الهاتف المحمول
- [ ] تقارير متقدمة مع رسوم بيانية
- [ ] نظام الموافقات المتدرج
- [ ] تكامل مع البنوك
- [ ] نظام إدارة المخزون
- [ ] واجهة ويب

## المساهمة في التطوير

نرحب بالمساهمات في تطوير هذا المشروع:
1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. تطبيق التغييرات
4. إرسال Pull Request

---

**ملاحظة:** تأكد من تشغيل MongoDB قبل بدء التطبيق، وتأكد من تثبيت جميع المتطلبات المذكورة في ملف requirements.txt