# إعداد MongoDB للتطبيق المحاسبي

## تثبيت MongoDB على Windows

### 1. تحميل MongoDB
- اذهب إلى: https://www.mongodb.com/try/download/community
- اختر Windows
- حمل النسخة الأحدث

### 2. تثبيت MongoDB
1. شغل ملف التثبيت
2. اختر "Complete" installation
3. اختر "Install MongoDB as a Service"
4. اختر "Run service as Network Service user"
5. اختر "Install MongoDB Compass" (اختياري)

### 3. التحقق من التثبيت
افتح Command Prompt وشغل:
```cmd
mongod --version
```

### 4. تشغيل MongoDB
MongoDB سيعمل تلقائياً كخدمة Windows. للتحقق:
```cmd
net start MongoDB
```

## إعداد قاعدة البيانات

### 1. الاتصال بـ MongoDB
```cmd
mongo
```

### 2. إنشاء قاعدة البيانات
```javascript
use accounting_system
```

### 3. إنشاء مستخدم قاعدة البيانات (اختياري)
```javascript
db.createUser({
  user: "accounting_admin",
  pwd: "secure_password",
  roles: [
    { role: "readWrite", db: "accounting_system" }
  ]
})
```

## إعداد MongoDB على macOS

### 1. تثبيت باستخدام Homebrew
```bash
brew tap mongodb/brew
brew install mongodb-community
```

### 2. تشغيل MongoDB
```bash
brew services start mongodb/brew/mongodb-community
```

## إعداد MongoDB على Linux (Ubuntu)

### 1. إضافة مفتاح GPG
```bash
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
```

### 2. إضافة المستودع
```bash
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
```

### 3. تحديث وتثبيت
```bash
sudo apt-get update
sudo apt-get install -y mongodb-org
```

### 4. تشغيل MongoDB
```bash
sudo systemctl start mongod
sudo systemctl enable mongod
```

## التحقق من عمل MongoDB

### 1. اختبار الاتصال
```python
from pymongo import MongoClient

try:
    client = MongoClient('mongodb://localhost:27017/')
    client.server_info()
    print("✅ MongoDB يعمل بنجاح")
except Exception as e:
    print(f"❌ خطأ في الاتصال: {e}")
```

### 2. عرض قواعد البيانات
```cmd
mongo --eval "db.adminCommand('listDatabases')"
```

## إعدادات الأمان (للإنتاج)

### 1. تفعيل المصادقة
في ملف `/etc/mongod.conf`:
```yaml
security:
  authorization: enabled
```

### 2. إنشاء مستخدم مدير
```javascript
use admin
db.createUser({
  user: "admin",
  pwd: "strong_password",
  roles: [ { role: "userAdminAnyDatabase", db: "admin" } ]
})
```

## استكشاف الأخطاء

### خطأ: MongoDB لا يعمل
```bash
# Windows
net start MongoDB

# macOS
brew services start mongodb/brew/mongodb-community

# Linux
sudo systemctl start mongod
```

### خطأ: فشل الاتصال
1. تأكد من تشغيل MongoDB
2. تحقق من المنفذ (27017 افتراضياً)
3. تحقق من إعدادات الجدار الناري

### خطأ: مساحة القرص ممتلئة
```bash
# تنظيف ملفات السجل
db.runCommand({logRotate: 1})

# ضغط قاعدة البيانات
db.runCommand({compact: "collection_name"})
```

## أدوات مفيدة

### 1. MongoDB Compass
واجهة رسومية لإدارة MongoDB
- تحميل: https://www.mongodb.com/products/compass

### 2. Studio 3T
أداة متقدمة لإدارة MongoDB
- تحميل: https://studio3t.com/

### 3. Robo 3T
أداة مجانية لإدارة MongoDB
- تحميل: https://robomongo.org/

## النسخ الاحتياطي

### إنشاء نسخة احتياطية
```bash
mongodump --db accounting_system --out /path/to/backup
```

### استعادة النسخة الاحتياطية
```bash
mongorestore --db accounting_system /path/to/backup/accounting_system
```

## مراقبة الأداء

### عرض الإحصائيات
```javascript
db.stats()
db.serverStatus()
```

### مراقبة العمليات النشطة
```javascript
db.currentOp()
```

---

**ملاحظة:** تأكد من تشغيل MongoDB قبل بدء التطبيق المحاسبي