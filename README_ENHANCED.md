# نظام المحاسبة المطور - الإصدار المحسن

## 🚀 نظرة عامة

نظام محاسبة شامل ومطور باللغة العربية مع واجهة مستخدم حديثة، يدعم إدارة العملاء والموظفين والفواتير المتقدمة.

## ✨ الميزات الجديدة المضافة

### 🏢 إدارة العملاء الشاملة
- **إضافة وتعديل العملاء**: نظام كامل لإدارة بيانات العملاء
- **تتبع الأرصدة**: مراقبة أرصدة العملاء والمديونيات
- **بيانات الاتصال**: إدارة معلومات الاتصال والشركات
- **تصدير البيانات**: تصدير قوائم العملاء إلى Excel

### 👥 إدارة الموظفين المتقدمة
- **ملفات الموظفين**: إدارة شاملة لبيانات الموظفين
- **نظام الرواتب**: حساب وإدارة رواتب الموظفين الشهرية
- **إدارة الحضور**: تتبع حضور وغياب الموظفين
- **إدارة الإجازات**: نظام طلبات الإجازات والموافقات
- **التقارير**: تقارير شاملة للموارد البشرية

### 🧾 نظام الفواتير المتقدم
- **فواتير تفصيلية**: إنشاء فواتير مع أصناف متعددة
- **حساب الضرائب**: حساب تلقائي لضريبة القيمة المضافة (15%)
- **ربط العملاء**: ربط الفواتير بقاعدة بيانات العملاء
- **إدارة المنتجات**: كتالوج منتجات مع الأسعار والكميات
- **حالات الفواتير**: تتبع حالة الفواتير (مدفوعة، معلقة، ملغاة)

## 🎯 الواجهات المحدثة

### 👨‍💼 واجهة المدير
- **إدارة العملاء**: إضافة، تعديل، وعرض بيانات العملاء
- **إدارة الموظفين**: نظام شامل للموارد البشرية
- **الفواتير المتقدمة**: إنشاء وإدارة الفواتير التفصيلية
- **التقارير المطورة**: تقارير شاملة لجميع الأقسام

### 👨‍💻 واجهة المحاسب
- **عرض العملاء**: الاطلاع على قاعدة بيانات العملاء
- **عرض الفواتير**: مراجعة الفواتير والمعاملات
- **إنشاء فواتير**: إمكانية إنشاء فواتير جديدة
- **التقارير المالية**: تقارير مالية مفصلة

## 🗄️ قاعدة البيانات المطورة

### المجموعات الجديدة
```
- customers: بيانات العملاء
- employees: بيانات الموظفين  
- invoices: الفواتير الرئيسية
- invoice_items: أصناف الفواتير
- products: كتالوج المنتجات
- payroll: رواتب الموظفين
- attendance: سجلات الحضور
- leaves: طلبات الإجازات
```

## 🛠️ التقنيات المستخدمة

- **Python 3.13+**: لغة البرمجة الأساسية
- **CustomTkinter**: واجهة المستخدم الحديثة
- **MongoDB**: قاعدة البيانات NoSQL
- **bcrypt**: تشفير كلمات المرور
- **ReportLab**: إنشاء تقارير PDF
- **OpenPyXL**: تصدير ملفات Excel
- **Pandas**: معالجة البيانات

## 📋 متطلبات التشغيل

```bash
pip install customtkinter pymongo bcrypt Pillow reportlab openpyxl pandas matplotlib
```

## 🚀 طريقة التشغيل

### التشغيل المباشر
```bash
python accounting_app.py
```

### التشغيل مع الاختبار
```bash
python test_import.py
python run_app.py
```

## 👤 بيانات الدخول الافتراضية

### المدير
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### المحاسب  
- **اسم المستخدم**: accountant
- **كلمة المرور**: acc123

### المورد
- **اسم المستخدم**: supplier
- **كلمة المرور**: sup123

## 📊 الإحصائيات

- **إجمالي الأكواد**: 5500+ سطر
- **عدد الواجهات**: 3 واجهات كاملة
- **عدد المجموعات**: 12 مجموعة في قاعدة البيانات
- **عدد الميزات**: 50+ ميزة شاملة

## 🔧 الميزات التقنية

- **أمان متقدم**: تشفير كلمات المرور وحماية البيانات
- **واجهة عربية**: دعم كامل للغة العربية RTL
- **تصميم متجاوب**: واجهة تتكيف مع أحجام الشاشات
- **نظام إشعارات**: إشعارات سطح المكتب والتطبيق
- **تصدير متقدم**: تصدير إلى PDF وExcel
- **نسخ احتياطي**: نظام حفظ وإدارة البيانات

## 📈 التطويرات المستقبلية

- [ ] تطبيق ويب متكامل
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع
- [ ] ذكاء اصطناعي للتحليلات
- [ ] تقارير تفاعلية متقدمة

## 📞 الدعم والمساعدة

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2025-07-02  
**الإصدار**: 2.0 Enhanced
