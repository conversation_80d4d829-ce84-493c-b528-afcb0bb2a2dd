#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استيراد وتشغيل نظام المحاسبة
"""

def test_imports():
    """اختبار استيراد المكتبات"""
    print("🔍 اختبار المكتبات المطلوبة...")
    
    try:
        import customtkinter as ctk
        print("✅ CustomTkinter متوفر")
    except ImportError as e:
        print(f"❌ CustomTkinter غير متوفر: {e}")
        return False
    
    try:
        import pymongo
        print("✅ PyMongo متوفر")
    except ImportError as e:
        print(f"❌ PyMongo غير متوفر: {e}")
        return False
    
    try:
        import bcrypt
        print("✅ bcrypt متوفر")
    except ImportError as e:
        print(f"❌ bcrypt غير متوفر: {e}")
        return False
    
    try:
        import tkinter
        print("✅ Tkinter متوفر")
    except ImportError as e:
        print(f"❌ Tkinter غير متوفر: {e}")
        return False
    
    return True

def test_accounting_app():
    """اختبار استيراد التطبيق"""
    print("\n📱 اختبار استيراد التطبيق...")
    
    try:
        import accounting_app
        print("✅ تم استيراد accounting_app بنجاح")
        
        # اختبار إنشاء كائن التطبيق
        app = accounting_app.AccountingApp()
        print("✅ تم إنشاء كائن التطبيق بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار نظام المحاسبة...\n")
    
    # اختبار المكتبات
    if not test_imports():
        print("\n❌ فشل في اختبار المكتبات")
        return False
    
    # اختبار التطبيق
    if not test_accounting_app():
        print("\n❌ فشل في اختبار التطبيق")
        return False
    
    print("\n✅ جميع الاختبارات نجحت!")
    print("📱 يمكنك الآن تشغيل التطبيق باستخدام: python accounting_app.py")
    
    return True

if __name__ == "__main__":
    main()
