#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق محاسبي شامل - نظام إدارة محاسبي متكامل
المطور: فريق التطوير
الإصدار: 1.0.0

الوحدات الرئيسية:
1. واجهة المدير (Admin Page)
2. واجهة المحاسب (Accountant Page)  
3. واجهة المورد (Supplier Page)

المتطلبات:
- Python 3.8+
- MongoDB
- customtkinter
- pymongo
- bcrypt
- reportlab
- openpyxl
- pandas
- matplotlib
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk, filedialog
import pymongo
from pymongo import MongoClient
import bcrypt
from datetime import datetime, timezone, timedelta
import threading
import json
import os
from pathlib import Path
import logging
import secrets
import re

# ===========================
# إعدادات التطبيق
# ===========================

class Config:
    """إعدادات التطبيق"""
    APP_NAME = "نظام المحاسبة المتكامل"
    APP_VERSION = "1.0.0"
    
    # إعدادات قاعدة البيانات
    MONGODB_URI = "mongodb://localhost:27017/"
    DATABASE_NAME = "accounting_system"
    
    # إعدادات الواجهة
    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800
    
    # الألوان
    PRIMARY_COLOR = "#1f538d"
    SUCCESS_COLOR = "#28a745"
    ERROR_COLOR = "#dc3545"
    
    # العملة
    CURRENCY = "ر.س"

# ===========================
# مدير قاعدة البيانات
# ===========================

class DatabaseManager:
    def __init__(self):
        """تهيئة مدير قاعدة البيانات"""
        self.client = None
        self.db = None
        self.connect()
        self._setup_collections()
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.client = MongoClient(Config.MONGODB_URI, serverSelectionTimeoutMS=5000)
            self.client.server_info()
            self.db = self.client[Config.DATABASE_NAME]
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return True
        except Exception as e:
            print(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
            return False
    
    def _setup_collections(self):
        """إعداد المجموعات"""
        try:
            self.users_collection = self.db.users
            self.transactions_collection = self.db.transactions
            self.accounts_collection = self.db.accounts
            self.suppliers_collection = self.db.suppliers
            self.invoices_collection = self.db.invoices
            self.payments_collection = self.db.payments
            self.notifications_collection = self.db.notifications
            self.reports_collection = self.db.reports

            # المجموعات الجديدة
            self.customers_collection = self.db.customers
            self.employees_collection = self.db.employees
            self.invoice_items_collection = self.db.invoice_items
            self.products_collection = self.db.products
            self.attendance_collection = self.db.attendance
            self.payroll_collection = self.db.payroll
            self.leaves_collection = self.db.leaves

            # إنشاء فهارس
            self.users_collection.create_index("username", unique=True)
            self.accounts_collection.create_index("account_code", unique=True)
            self.suppliers_collection.create_index("supplier_code", unique=True)
            self.invoices_collection.create_index("invoice_number", unique=True)

            # فهارس المجموعات الجديدة
            self.customers_collection.create_index("customer_code", unique=True)
            self.employees_collection.create_index("employee_id", unique=True)
            self.products_collection.create_index("product_code", unique=True)

        except Exception as e:
            print(f"❌ خطأ في إعداد المجموعات: {e}")
    
    def get_current_time(self):
        """الحصول على الوقت الحالي"""
        return datetime.now(timezone.utc)
    
    def test_connection(self):
        """اختبار الاتصال"""
        try:
            self.client.server_info()
            return True
        except:
            return False
    
    def close_connection(self):
        """إغلاق الاتصال"""
        if self.client:
            self.client.close()

# ===========================
# نظام الإشعارات
# ===========================

class NotificationManager:
    """مدير الإشعارات"""

    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.notifications = []

    def add_notification(self, title, message, notification_type="info", user_id=None):
        """إضافة إشعار جديد"""
        notification = {
            "id": str(datetime.now().timestamp()),
            "title": title,
            "message": message,
            "type": notification_type,  # info, warning, error, success
            "user_id": user_id,
            "created_at": datetime.now(),
            "read": False
        }

        self.notifications.append(notification)

        # حفظ في قاعدة البيانات
        try:
            self.db_manager.notifications_collection.insert_one(notification)
        except:
            pass  # في حالة عدم توفر قاعدة البيانات

        return notification

    def get_notifications(self, user_id=None, unread_only=False):
        """الحصول على الإشعارات"""
        try:
            query = {}
            if user_id:
                query["user_id"] = user_id
            if unread_only:
                query["read"] = False

            notifications = list(self.db_manager.notifications_collection.find(query).sort("created_at", -1))
            return notifications
        except:
            # في حالة عدم توفر قاعدة البيانات، استخدم الإشعارات المحلية
            filtered_notifications = self.notifications
            if user_id:
                filtered_notifications = [n for n in filtered_notifications if n.get("user_id") == user_id]
            if unread_only:
                filtered_notifications = [n for n in filtered_notifications if not n.get("read", False)]
            return filtered_notifications

    def mark_as_read(self, notification_id):
        """تمييز الإشعار كمقروء"""
        try:
            self.db_manager.notifications_collection.update_one(
                {"id": notification_id},
                {"$set": {"read": True}}
            )
        except:
            # تحديث الإشعارات المحلية
            for notification in self.notifications:
                if notification["id"] == notification_id:
                    notification["read"] = True
                    break

    def get_unread_count(self, user_id=None):
        """الحصول على عدد الإشعارات غير المقروءة"""
        unread_notifications = self.get_notifications(user_id, unread_only=True)
        return len(unread_notifications)

    def show_desktop_notification(self, title, message):
        """عرض إشعار سطح المكتب"""
        try:
            # محاولة استخدام مكتبة plyer للإشعارات
            import plyer
            plyer.notification.notify(
                title=title,
                message=message,
                app_name="نظام المحاسبة",
                timeout=5
            )
        except ImportError:
            # في حالة عدم توفر المكتبة، استخدم messagebox
            messagebox.showinfo(title, message)
        except Exception as e:
            print(f"خطأ في عرض الإشعار: {e}")

# ===========================
# نظام المصادقة
# ===========================

class AuthManager:
    def __init__(self, db_manager):
        """تهيئة مدير المصادقة"""
        self.db_manager = db_manager
        self.current_user = None
        self.session_token = None
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password, hashed_password):
        """التحقق من كلمة المرور"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
        except:
            return False
    
    def login(self, username, password):
        """تسجيل الدخول"""
        try:
            user = self.db_manager.users_collection.find_one({
                "username": username,
                "is_active": True
            })
            
            if not user:
                return None
            
            if not self.verify_password(password, user["password"]):
                return None
            
            self.current_user = user
            self.session_token = secrets.token_urlsafe(32)
            
            # تحديث آخر تسجيل دخول
            self.db_manager.users_collection.update_one(
                {"_id": user["_id"]},
                {"$set": {"last_login": self.db_manager.get_current_time()}}
            )
            
            return {
                "user_id": str(user["_id"]),
                "username": user["username"],
                "role": user["role"],
                "full_name": user["full_name"],
                "email": user["email"]
            }
            
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            return None
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None
        self.session_token = None
    
    def is_authenticated(self):
        """التحقق من صحة الجلسة"""
        return self.current_user is not None

# ===========================
# نافذة تسجيل الدخول
# ===========================

class LoginWindow:
    def __init__(self, db_manager):
        """تهيئة نافذة تسجيل الدخول"""
        self.db_manager = db_manager
        self.auth_manager = AuthManager(db_manager)
        
        self.root = ctk.CTk()
        self.root.title(f"{Config.APP_NAME} - تسجيل الدخول")
        self.root.geometry("400x500")
        self.root.resizable(False, False)
        
        self.center_window()
        self.create_widgets()
        
        self.login_attempts = 0
        self.max_attempts = 5
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # شعار التطبيق
        title_label = ctk.CTkLabel(
            main_frame,
            text=Config.APP_NAME,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(30, 10))
        
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="نظام إدارة محاسبي متكامل",
            font=ctk.CTkFont(size=14)
        )
        subtitle_label.pack(pady=(0, 30))
        
        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(main_frame)
        login_frame.pack(fill="x", padx=20, pady=20)
        
        login_title = ctk.CTkLabel(
            login_frame,
            text="تسجيل الدخول",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        login_title.pack(pady=(20, 10))
        
        # حقل اسم المستخدم
        username_label = ctk.CTkLabel(login_frame, text="اسم المستخدم:")
        username_label.pack(pady=(10, 5))
        
        self.username_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="أدخل اسم المستخدم",
            width=250,
            height=35
        )
        self.username_entry.pack(pady=(0, 10))
        
        # حقل كلمة المرور
        password_label = ctk.CTkLabel(login_frame, text="كلمة المرور:")
        password_label.pack(pady=(10, 5))
        
        self.password_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="أدخل كلمة المرور",
            show="*",
            width=250,
            height=35
        )
        self.password_entry.pack(pady=(0, 10))
        
        # زر تسجيل الدخول
        self.login_button = ctk.CTkButton(
            login_frame,
            text="تسجيل الدخول",
            command=self.login,
            width=250,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.login_button.pack(pady=(20, 10))
        
        # معلومات المدير الافتراضي
        info_label = ctk.CTkLabel(
            login_frame,
            text="المدير الافتراضي:\nاسم المستخدم: admin\nكلمة المرور: admin123",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        info_label.pack(pady=(10, 20))
        
        # حالة الاتصال
        self.connection_status = ctk.CTkLabel(
            main_frame,
            text="",
            font=ctk.CTkFont(size=10)
        )
        self.connection_status.pack(pady=(10, 0))
        
        self.check_database_connection()
        
        # ربط مفتاح Enter
        self.root.bind('<Return>', lambda event: self.login())
        self.username_entry.focus()
    
    def check_database_connection(self):
        """التحقق من حالة الاتصال"""
        def check():
            if self.db_manager.test_connection():
                self.connection_status.configure(
                    text="✅ متصل بقاعدة البيانات",
                    text_color="green"
                )
            else:
                self.connection_status.configure(
                    text="❌ فشل الاتصال بقاعدة البيانات",
                    text_color="red"
                )
        
        threading.Thread(target=check, daemon=True).start()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        if self.login_attempts >= self.max_attempts:
            messagebox.showerror("تم حظر الحساب", "تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول")
            return
        
        self.login_button.configure(state="disabled", text="جاري التحقق...")
        
        def perform_login():
            try:
                user_data = self.auth_manager.login(username, password)
                
                if user_data:
                    self.root.after(0, lambda: self.login_success(user_data))
                else:
                    self.login_attempts += 1
                    remaining = self.max_attempts - self.login_attempts
                    error_msg = f"اسم المستخدم أو كلمة المرور غير صحيحة\nالمحاولات المتبقية: {remaining}"
                    self.root.after(0, lambda: self.login_failed(error_msg))
                    
            except Exception as e:
                self.root.after(0, lambda: self.login_failed(f"خطأ في تسجيل الدخول: {str(e)}"))
        
        threading.Thread(target=perform_login, daemon=True).start()
    
    def login_success(self, user_data):
        """معالجة نجاح تسجيل الدخول"""
        self.login_button.configure(state="normal", text="تسجيل الدخول")
        self.root.withdraw()
        self.open_main_interface(user_data)
    
    def login_failed(self, error_message):
        """معالجة فشل تسجيل الدخول"""
        self.login_button.configure(state="normal", text="تسجيل الدخول")
        messagebox.showerror("فشل تسجيل الدخول", error_message)
        self.password_entry.delete(0, 'end')
        self.password_entry.focus()
    
    def open_main_interface(self, user_data):
        """فتح الواجهة المناسبة"""
        role = user_data["role"]
        
        try:
            if role == "admin":
                admin_window = AdminWindow(self.db_manager, self.auth_manager, user_data)
                admin_window.show()
            elif role == "accountant":
                accountant_window = AccountantWindow(self.db_manager, self.auth_manager, user_data)
                accountant_window.show()
            elif role == "supplier":
                supplier_window = SupplierWindow(self.db_manager, self.auth_manager, user_data)
                supplier_window.show()
            else:
                messagebox.showerror("خطأ", f"نوع المستخدم غير مدعوم: {role}")
                self.root.deiconify()
                return
            
            self.root.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح الواجهة: {str(e)}")
            self.root.deiconify()
    
    def run(self):
        """تشغيل النافذة"""
        try:
            self.root.mainloop()
        except Exception as e:
            print(f"خطأ في تشغيل النافذة: {e}")
        finally:
            if hasattr(self, 'root') and self.root.winfo_exists():
                self.root.destroy()

# ===========================
# واجهة المدير
# ===========================

class AdminWindow:
    def __init__(self, db_manager, auth_manager, user_data):
        """تهيئة واجهة المدير"""
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.user_data = user_data
        self.notification_manager = NotificationManager(db_manager)

        self.root = ctk.CTk()
        self.root.title(f"{Config.APP_NAME} - لوحة تحكم المدير")
        self.root.geometry(f"{Config.WINDOW_WIDTH}x{Config.WINDOW_HEIGHT}")

        self.center_window()
        self.create_widgets()
        self.refresh_data()

        # إضافة إشعار ترحيبي
        self.notification_manager.add_notification(
            "مرحباً بك",
            f"مرحباً {self.user_data['full_name']}، تم تسجيل دخولك بنجاح",
            "success",
            self.user_data.get('_id')
        )
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الشريط العلوي
        self.create_header()
        
        # الشريط الجانبي والمحتوى
        content_frame = ctk.CTkFrame(self.root)
        content_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # الشريط الجانبي
        self.create_sidebar(content_frame)
        
        # المحتوى الرئيسي
        self.create_main_content(content_frame)
    
    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = ctk.CTkFrame(self.root, height=60)
        header_frame.pack(fill="x", padx=10, pady=10)
        header_frame.pack_propagate(False)
        
        # عنوان التطبيق
        title_label = ctk.CTkLabel(
            header_frame,
            text=f"مرحباً {self.user_data['full_name']} - لوحة تحكم المدير",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=15)
        
        # أزرار التحكم
        controls_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        controls_frame.pack(side="right", padx=20, pady=10)
        
        # زر الإشعارات
        self.notifications_button = ctk.CTkButton(
            controls_frame,
            text="🔔 الإشعارات (0)",
            command=self.show_notifications,
            width=120,
            height=35
        )
        self.notifications_button.pack(side="right", padx=5)
        
        # زر تسجيل الخروج
        logout_button = ctk.CTkButton(
            controls_frame,
            text="تسجيل الخروج",
            command=self.logout,
            width=100,
            height=35,
            fg_color="red",
            hover_color="darkred"
        )
        logout_button.pack(side="right", padx=5)
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        self.sidebar_frame = ctk.CTkFrame(parent, width=200)
        self.sidebar_frame.pack(side="left", fill="y", padx=(0, 10))
        self.sidebar_frame.pack_propagate(False)
        
        # عنوان القائمة
        menu_title = ctk.CTkLabel(
            self.sidebar_frame,
            text="القائمة الرئيسية",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        menu_title.pack(pady=(20, 10))
        
        # أزرار القائمة
        self.menu_buttons = {}
        
        menu_items = [
            ("📊 لوحة المعلومات", "dashboard"),
            ("👥 إدارة المستخدمين", "users"),
            ("👤 إدارة العملاء", "customers"),
            ("👨‍💼 إدارة الموظفين", "employees"),
            ("📈 التقارير المالية", "reports"),
            ("💰 المعاملات", "transactions"),
            ("🏪 الموردين", "suppliers"),
            ("🧾 الفواتير", "invoices"),
            ("💳 المدفوعات", "payments"),
            ("🔧 إعدادات النظام", "settings")
        ]
        
        for text, key in menu_items:
            button = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                command=lambda k=key: self.show_section(k),
                width=180,
                height=40,
                anchor="w"
            )
            button.pack(pady=5, padx=10)
            self.menu_buttons[key] = button
        
        self.current_section = "dashboard"
        self.menu_buttons["dashboard"].configure(fg_color="gray")
    
    def create_main_content(self, parent):
        """إنشاء المحتوى الرئيسي"""
        self.main_frame = ctk.CTkFrame(parent)
        self.main_frame.pack(side="right", fill="both", expand=True)
        
        # لوحة المعلومات الافتراضية
        self.create_dashboard()
    
    def create_dashboard(self):
        """إنشاء لوحة المعلومات"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # عنوان اللوحة
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📊 لوحة المعلومات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # الإحصائيات السريعة
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="الإحصائيات السريعة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        stats_title.pack(pady=(15, 10))
        
        # بطاقات الإحصائيات
        cards_frame = ctk.CTkFrame(stats_frame)
        cards_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        cards_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # بطاقة المستخدمين
        self.create_stat_card(cards_frame, "👥", "إجمالي المستخدمين", "0", 0, 0)
        
        # بطاقة المعاملات
        self.create_stat_card(cards_frame, "💰", "إجمالي المعاملات", "0", 0, 1)
        
        # بطاقة الموردين
        self.create_stat_card(cards_frame, "🏪", "الموردين النشطين", "0", 0, 2)
        
        # بطاقة الفواتير
        self.create_stat_card(cards_frame, "🧾", "الفواتير المعلقة", "0", 0, 3)
        
        # الأنشطة الأخيرة
        activities_frame = ctk.CTkFrame(self.main_frame)
        activities_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        activities_title = ctk.CTkLabel(
            activities_frame,
            text="الأنشطة الأخيرة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        activities_title.pack(pady=(15, 10))
        
        # قائمة الأنشطة
        self.activities_list = ctk.CTkScrollableFrame(activities_frame, height=200)
        self.activities_list.pack(fill="both", expand=True, padx=20, pady=(0, 20))
    
    def create_stat_card(self, parent, icon, title, value, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # الأيقونة
        icon_label = ctk.CTkLabel(
            card_frame,
            text=icon,
            font=ctk.CTkFont(size=30)
        )
        icon_label.pack(pady=(15, 5))
        
        # القيمة
        value_label = ctk.CTkLabel(
            card_frame,
            text=value,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        value_label.pack(pady=5)
        
        # العنوان
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=12)
        )
        title_label.pack(pady=(0, 15))
    
    def show_section(self, section_key):
        """عرض قسم معين"""
        # إعادة تعيين ألوان الأزرار
        for key, button in self.menu_buttons.items():
            if key == section_key:
                button.configure(fg_color="gray")
            else:
                button.configure(fg_color=["#3B8ED0", "#1F6AA5"])
        
        self.current_section = section_key
        
        # عرض المحتوى المناسب
        if section_key == "dashboard":
            self.create_dashboard()
        elif section_key == "users":
            self.create_users_management()
        elif section_key == "customers":
            self.create_customers_management()
        elif section_key == "employees":
            self.create_employees_management()
        elif section_key == "reports":
            self.create_reports_section()
        elif section_key == "transactions":
            self.create_admin_transactions_section()
        elif section_key == "suppliers":
            self.create_suppliers_management()
        elif section_key == "invoices":
            self.create_advanced_invoices_section()
        elif section_key == "payments":
            self.create_admin_payments_section()
        elif section_key == "settings":
            self.create_admin_settings_section()
        else:
            self.create_placeholder_section(section_key)
    
    def create_users_management(self):
        """إنشاء قسم إدارة المستخدمين"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="👥 إدارة المستخدمين",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)
        
        add_user_button = ctk.CTkButton(
            controls_frame,
            text="➕ إضافة مستخدم جديد",
            command=self.add_user_dialog,
            width=150,
            height=35
        )
        add_user_button.pack(side="left", padx=10, pady=10)
        
        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_users_list,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)
        
        # جدول المستخدمين
        users_frame = ctk.CTkFrame(self.main_frame)
        users_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إنشاء Treeview للجدول
        columns = ("الاسم الكامل", "اسم المستخدم", "الدور", "البريد الإلكتروني", "الحالة", "تاريخ الإنشاء")
        
        self.users_tree = ttk.Treeview(users_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=150)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(users_frame, orient="vertical", command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        # تعبئة الجدول
        self.users_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)
        
        # تحديث قائمة المستخدمين
        self.refresh_users_list()
    
    def add_user_dialog(self):
        """نافذة إضافة مستخدم جديد"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إضافة مستخدم جديد")
        dialog.geometry("400x500")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="إضافة مستخدم جديد",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="x", padx=20, pady=10)
        
        # الاسم الكامل
        ctk.CTkLabel(fields_frame, text="الاسم الكامل:").pack(pady=(10, 5))
        full_name_entry = ctk.CTkEntry(fields_frame, width=300)
        full_name_entry.pack(pady=(0, 10))
        
        # اسم المستخدم
        ctk.CTkLabel(fields_frame, text="اسم المستخدم:").pack(pady=(10, 5))
        username_entry = ctk.CTkEntry(fields_frame, width=300)
        username_entry.pack(pady=(0, 10))
        
        # كلمة المرور
        ctk.CTkLabel(fields_frame, text="كلمة المرور:").pack(pady=(10, 5))
        password_entry = ctk.CTkEntry(fields_frame, width=300, show="*")
        password_entry.pack(pady=(0, 10))
        
        # البريد الإلكتروني
        ctk.CTkLabel(fields_frame, text="البريد الإلكتروني:").pack(pady=(10, 5))
        email_entry = ctk.CTkEntry(fields_frame, width=300)
        email_entry.pack(pady=(0, 10))
        
        # رقم الهاتف
        ctk.CTkLabel(fields_frame, text="رقم الهاتف:").pack(pady=(10, 5))
        phone_entry = ctk.CTkEntry(fields_frame, width=300)
        phone_entry.pack(pady=(0, 10))
        
        # الدور
        ctk.CTkLabel(fields_frame, text="الدور:").pack(pady=(10, 5))
        role_var = ctk.StringVar(value="accountant")
        role_menu = ctk.CTkOptionMenu(
            fields_frame,
            values=["admin", "accountant", "supplier"],
            variable=role_var,
            width=300
        )
        role_menu.pack(pady=(0, 20))
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)
        
        def save_user():
            # التحقق من البيانات
            full_name = full_name_entry.get().strip()
            username = username_entry.get().strip()
            password = password_entry.get().strip()
            email = email_entry.get().strip()
            phone = phone_entry.get().strip()
            role = role_var.get()
            
            if not all([full_name, username, password, email]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            try:
                # التحقق من عدم وجود اسم المستخدم
                existing_user = self.db_manager.users_collection.find_one({"username": username})
                if existing_user:
                    messagebox.showerror("خطأ", "اسم المستخدم موجود بالفعل")
                    return
                
                # تشفير كلمة المرور
                hashed_password = self.auth_manager.hash_password(password)
                
                # إنشاء المستخدم
                user_data = {
                    "username": username,
                    "password": hashed_password,
                    "role": role,
                    "full_name": full_name,
                    "email": email,
                    "phone": phone,
                    "created_at": self.db_manager.get_current_time(),
                    "is_active": True
                }
                
                self.db_manager.users_collection.insert_one(user_data)
                
                messagebox.showinfo("نجح", "تم إضافة المستخدم بنجاح")
                dialog.destroy()
                self.refresh_users_list()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة المستخدم: {str(e)}")
        
        save_button = ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_user,
            width=100,
            height=35
        )
        save_button.pack(side="left", padx=10, pady=10)
        
        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=10, pady=10)
    
    def refresh_users_list(self):
        """تحديث قائمة المستخدمين"""
        try:
            # مسح البيانات السابقة
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
            
            # جلب المستخدمين من قاعدة البيانات
            users = list(self.db_manager.users_collection.find())
            
            for user in users:
                status = "نشط" if user.get("is_active", True) else "غير نشط"
                created_date = user.get("created_at", datetime.now()).strftime("%Y-%m-%d")
                
                self.users_tree.insert("", "end", values=(
                    user.get("full_name", ""),
                    user.get("username", ""),
                    user.get("role", ""),
                    user.get("email", ""),
                    status,
                    created_date
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث قائمة المستخدمين: {str(e)}")

    def create_customers_management(self):
        """إنشاء قسم إدارة العملاء"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="👤 إدارة العملاء",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        add_customer_button = ctk.CTkButton(
            controls_frame,
            text="➕ إضافة عميل جديد",
            command=self.add_customer_dialog,
            width=150,
            height=35
        )
        add_customer_button.pack(side="left", padx=10, pady=10)

        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_customers_list,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)

        export_button = ctk.CTkButton(
            controls_frame,
            text="📊 تصدير قائمة العملاء",
            command=self.export_customers_report,
            width=150,
            height=35
        )
        export_button.pack(side="left", padx=10, pady=10)

        # جدول العملاء
        customers_frame = ctk.CTkFrame(self.main_frame)
        customers_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("كود العميل", "اسم العميل", "الشركة", "الهاتف", "البريد الإلكتروني", "الرصيد", "تاريخ التسجيل")

        self.customers_tree = ttk.Treeview(customers_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(customers_frame, orient="vertical", command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.customers_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # ربط النقر المزدوج لتعديل العميل
        self.customers_tree.bind("<Double-1>", self.edit_customer_dialog)

        # تحديث قائمة العملاء
        self.refresh_customers_list()

    def add_customer_dialog(self):
        """نافذة إضافة عميل جديد"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إضافة عميل جديد")
        dialog.geometry("450x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="إضافة عميل جديد",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="x", padx=20, pady=10)

        # كود العميل
        ctk.CTkLabel(fields_frame, text="كود العميل:").pack(pady=(10, 5))
        customer_code_entry = ctk.CTkEntry(fields_frame, width=350)
        customer_code_entry.pack(pady=(0, 10))

        # اسم العميل
        ctk.CTkLabel(fields_frame, text="اسم العميل:").pack(pady=(10, 5))
        customer_name_entry = ctk.CTkEntry(fields_frame, width=350)
        customer_name_entry.pack(pady=(0, 10))

        # اسم الشركة
        ctk.CTkLabel(fields_frame, text="اسم الشركة:").pack(pady=(10, 5))
        company_name_entry = ctk.CTkEntry(fields_frame, width=350)
        company_name_entry.pack(pady=(0, 10))

        # رقم الهاتف
        ctk.CTkLabel(fields_frame, text="رقم الهاتف:").pack(pady=(10, 5))
        phone_entry = ctk.CTkEntry(fields_frame, width=350)
        phone_entry.pack(pady=(0, 10))

        # البريد الإلكتروني
        ctk.CTkLabel(fields_frame, text="البريد الإلكتروني:").pack(pady=(10, 5))
        email_entry = ctk.CTkEntry(fields_frame, width=350)
        email_entry.pack(pady=(0, 10))

        # العنوان
        ctk.CTkLabel(fields_frame, text="العنوان:").pack(pady=(10, 5))
        address_entry = ctk.CTkTextbox(fields_frame, width=350, height=60)
        address_entry.pack(pady=(0, 10))

        # الرصيد الافتتاحي
        ctk.CTkLabel(fields_frame, text="الرصيد الافتتاحي:").pack(pady=(10, 5))
        balance_entry = ctk.CTkEntry(fields_frame, width=350)
        balance_entry.insert(0, "0.00")
        balance_entry.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        def save_customer():
            # التحقق من البيانات
            customer_code = customer_code_entry.get().strip()
            customer_name = customer_name_entry.get().strip()
            company_name = company_name_entry.get().strip()
            phone = phone_entry.get().strip()
            email = email_entry.get().strip()
            address = address_entry.get("1.0", "end-1c").strip()
            balance_str = balance_entry.get().strip()

            if not all([customer_code, customer_name]):
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة (كود العميل واسم العميل)")
                return

            try:
                balance = float(balance_str) if balance_str else 0.0

                # التحقق من عدم وجود كود العميل
                existing_customer = self.db_manager.customers_collection.find_one({"customer_code": customer_code})
                if existing_customer:
                    messagebox.showerror("خطأ", "كود العميل موجود بالفعل")
                    return

                # إنشاء العميل
                customer_data = {
                    "customer_code": customer_code,
                    "customer_name": customer_name,
                    "company_name": company_name,
                    "phone": phone,
                    "email": email,
                    "address": address,
                    "balance": balance,
                    "created_at": self.db_manager.get_current_time(),
                    "is_active": True,
                    "total_purchases": 0.0,
                    "last_transaction_date": None
                }

                self.db_manager.customers_collection.insert_one(customer_data)

                # إضافة إشعار
                self.notification_manager.add_notification(
                    "عميل جديد",
                    f"تم إضافة العميل {customer_name} بنجاح",
                    "success",
                    self.user_data.get('user_id')
                )

                messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
                dialog.destroy()
                self.refresh_customers_list()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رصيد صحيح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة العميل: {str(e)}")

        save_button = ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_customer,
            width=100,
            height=35
        )
        save_button.pack(side="left", padx=10, pady=10)

        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=10, pady=10)

    def refresh_customers_list(self):
        """تحديث قائمة العملاء"""
        try:
            # مسح البيانات السابقة
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # جلب العملاء من قاعدة البيانات
            customers = list(self.db_manager.customers_collection.find().sort("created_at", -1))

            for customer in customers:
                created_date = customer.get("created_at", datetime.now()).strftime("%Y-%m-%d")
                balance = f"{customer.get('balance', 0):,.2f} ر.س"

                self.customers_tree.insert("", "end", values=(
                    customer.get("customer_code", ""),
                    customer.get("customer_name", ""),
                    customer.get("company_name", ""),
                    customer.get("phone", ""),
                    customer.get("email", ""),
                    balance,
                    created_date
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث قائمة العملاء: {str(e)}")

    def edit_customer_dialog(self, event):
        """نافذة تعديل عميل"""
        selection = self.customers_tree.selection()
        if not selection:
            return

        item = self.customers_tree.item(selection[0])
        customer_code = item['values'][0]

        # جلب بيانات العميل من قاعدة البيانات
        customer = self.db_manager.customers_collection.find_one({"customer_code": customer_code})
        if not customer:
            messagebox.showerror("خطأ", "لم يتم العثور على العميل")
            return

        dialog = ctk.CTkToplevel(self.root)
        dialog.title("تعديل بيانات العميل")
        dialog.geometry("450x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="تعديل بيانات العميل",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # حقول الإدخال مع البيانات الحالية
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="x", padx=20, pady=10)

        # كود العميل (غير قابل للتعديل)
        ctk.CTkLabel(fields_frame, text="كود العميل:").pack(pady=(10, 5))
        customer_code_entry = ctk.CTkEntry(fields_frame, width=350)
        customer_code_entry.insert(0, customer.get("customer_code", ""))
        customer_code_entry.configure(state="disabled")
        customer_code_entry.pack(pady=(0, 10))

        # اسم العميل
        ctk.CTkLabel(fields_frame, text="اسم العميل:").pack(pady=(10, 5))
        customer_name_entry = ctk.CTkEntry(fields_frame, width=350)
        customer_name_entry.insert(0, customer.get("customer_name", ""))
        customer_name_entry.pack(pady=(0, 10))

        # اسم الشركة
        ctk.CTkLabel(fields_frame, text="اسم الشركة:").pack(pady=(10, 5))
        company_name_entry = ctk.CTkEntry(fields_frame, width=350)
        company_name_entry.insert(0, customer.get("company_name", ""))
        company_name_entry.pack(pady=(0, 10))

        # رقم الهاتف
        ctk.CTkLabel(fields_frame, text="رقم الهاتف:").pack(pady=(10, 5))
        phone_entry = ctk.CTkEntry(fields_frame, width=350)
        phone_entry.insert(0, customer.get("phone", ""))
        phone_entry.pack(pady=(0, 10))

        # البريد الإلكتروني
        ctk.CTkLabel(fields_frame, text="البريد الإلكتروني:").pack(pady=(10, 5))
        email_entry = ctk.CTkEntry(fields_frame, width=350)
        email_entry.insert(0, customer.get("email", ""))
        email_entry.pack(pady=(0, 10))

        # العنوان
        ctk.CTkLabel(fields_frame, text="العنوان:").pack(pady=(10, 5))
        address_entry = ctk.CTkTextbox(fields_frame, width=350, height=60)
        address_entry.insert("1.0", customer.get("address", ""))
        address_entry.pack(pady=(0, 10))

        # الرصيد
        ctk.CTkLabel(fields_frame, text="الرصيد:").pack(pady=(10, 5))
        balance_entry = ctk.CTkEntry(fields_frame, width=350)
        balance_entry.insert(0, str(customer.get("balance", 0.0)))
        balance_entry.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        def update_customer():
            # التحقق من البيانات
            customer_name = customer_name_entry.get().strip()
            company_name = company_name_entry.get().strip()
            phone = phone_entry.get().strip()
            email = email_entry.get().strip()
            address = address_entry.get("1.0", "end-1c").strip()
            balance_str = balance_entry.get().strip()

            if not customer_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                return

            try:
                balance = float(balance_str) if balance_str else 0.0

                # تحديث بيانات العميل
                update_data = {
                    "customer_name": customer_name,
                    "company_name": company_name,
                    "phone": phone,
                    "email": email,
                    "address": address,
                    "balance": balance,
                    "updated_at": self.db_manager.get_current_time()
                }

                self.db_manager.customers_collection.update_one(
                    {"customer_code": customer_code},
                    {"$set": update_data}
                )

                # إضافة إشعار
                self.notification_manager.add_notification(
                    "تحديث عميل",
                    f"تم تحديث بيانات العميل {customer_name} بنجاح",
                    "info",
                    self.user_data.get('user_id')
                )

                messagebox.showinfo("نجح", "تم تحديث بيانات العميل بنجاح")
                dialog.destroy()
                self.refresh_customers_list()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رصيد صحيح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحديث العميل: {str(e)}")

        update_button = ctk.CTkButton(
            buttons_frame,
            text="تحديث",
            command=update_customer,
            width=100,
            height=35
        )
        update_button.pack(side="left", padx=10, pady=10)

        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=10, pady=10)

    def export_customers_report(self):
        """تصدير تقرير العملاء"""
        try:
            customers = list(self.db_manager.customers_collection.find())

            if not customers:
                messagebox.showinfo("تنبيه", "لا توجد بيانات عملاء للتصدير")
                return

            # محاكاة تصدير التقرير
            messagebox.showinfo("نجح", f"تم تصدير تقرير {len(customers)} عميل بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التقرير: {str(e)}")

    def create_employees_management(self):
        """إنشاء قسم إدارة الموظفين"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="👨‍💼 إدارة الموظفين",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        add_employee_button = ctk.CTkButton(
            controls_frame,
            text="➕ إضافة موظف جديد",
            command=self.add_employee_dialog,
            width=150,
            height=35
        )
        add_employee_button.pack(side="left", padx=10, pady=10)

        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_employees_list,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)

        payroll_button = ctk.CTkButton(
            controls_frame,
            text="💰 إدارة الرواتب",
            command=self.manage_payroll,
            width=120,
            height=35
        )
        payroll_button.pack(side="left", padx=10, pady=10)

        attendance_button = ctk.CTkButton(
            controls_frame,
            text="📅 إدارة الحضور",
            command=self.manage_attendance,
            width=120,
            height=35
        )
        attendance_button.pack(side="left", padx=10, pady=10)

        # جدول الموظفين
        employees_frame = ctk.CTkFrame(self.main_frame)
        employees_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("رقم الموظف", "اسم الموظف", "المنصب", "القسم", "الراتب", "الهاتف", "تاريخ التوظيف", "الحالة")

        self.employees_tree = ttk.Treeview(employees_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.employees_tree.heading(col, text=col)
            self.employees_tree.column(col, width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(employees_frame, orient="vertical", command=self.employees_tree.yview)
        self.employees_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.employees_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # ربط النقر المزدوج لتعديل الموظف
        self.employees_tree.bind("<Double-1>", self.edit_employee_dialog)

        # تحديث قائمة الموظفين
        self.refresh_employees_list()

    def add_employee_dialog(self):
        """نافذة إضافة موظف جديد"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إضافة موظف جديد")
        dialog.geometry("500x700")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="إضافة موظف جديد",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="x", padx=20, pady=10)

        # رقم الموظف
        ctk.CTkLabel(fields_frame, text="رقم الموظف:").pack(pady=(10, 5))
        employee_id_entry = ctk.CTkEntry(fields_frame, width=400)
        employee_id_entry.pack(pady=(0, 10))

        # اسم الموظف
        ctk.CTkLabel(fields_frame, text="اسم الموظف:").pack(pady=(10, 5))
        employee_name_entry = ctk.CTkEntry(fields_frame, width=400)
        employee_name_entry.pack(pady=(0, 10))

        # المنصب
        ctk.CTkLabel(fields_frame, text="المنصب:").pack(pady=(10, 5))
        position_entry = ctk.CTkEntry(fields_frame, width=400)
        position_entry.pack(pady=(0, 10))

        # القسم
        ctk.CTkLabel(fields_frame, text="القسم:").pack(pady=(10, 5))
        department_var = ctk.StringVar(value="المحاسبة")
        department_menu = ctk.CTkOptionMenu(
            fields_frame,
            values=["المحاسبة", "الإدارة", "المبيعات", "التسويق", "الموارد البشرية", "تقنية المعلومات"],
            variable=department_var,
            width=400
        )
        department_menu.pack(pady=(0, 10))

        # الراتب الأساسي
        ctk.CTkLabel(fields_frame, text="الراتب الأساسي:").pack(pady=(10, 5))
        salary_entry = ctk.CTkEntry(fields_frame, width=400)
        salary_entry.pack(pady=(0, 10))

        # رقم الهاتف
        ctk.CTkLabel(fields_frame, text="رقم الهاتف:").pack(pady=(10, 5))
        phone_entry = ctk.CTkEntry(fields_frame, width=400)
        phone_entry.pack(pady=(0, 10))

        # البريد الإلكتروني
        ctk.CTkLabel(fields_frame, text="البريد الإلكتروني:").pack(pady=(10, 5))
        email_entry = ctk.CTkEntry(fields_frame, width=400)
        email_entry.pack(pady=(0, 10))

        # تاريخ التوظيف
        ctk.CTkLabel(fields_frame, text="تاريخ التوظيف (YYYY-MM-DD):").pack(pady=(10, 5))
        hire_date_entry = ctk.CTkEntry(fields_frame, width=400)
        hire_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        hire_date_entry.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        def save_employee():
            # التحقق من البيانات
            employee_id = employee_id_entry.get().strip()
            employee_name = employee_name_entry.get().strip()
            position = position_entry.get().strip()
            department = department_var.get()
            salary_str = salary_entry.get().strip()
            phone = phone_entry.get().strip()
            email = email_entry.get().strip()
            hire_date_str = hire_date_entry.get().strip()

            if not all([employee_id, employee_name, position, salary_str]):
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
                return

            try:
                salary = float(salary_str)
                hire_date = datetime.strptime(hire_date_str, "%Y-%m-%d")

                # التحقق من عدم وجود رقم الموظف
                existing_employee = self.db_manager.employees_collection.find_one({"employee_id": employee_id})
                if existing_employee:
                    messagebox.showerror("خطأ", "رقم الموظف موجود بالفعل")
                    return

                # إنشاء الموظف
                employee_data = {
                    "employee_id": employee_id,
                    "employee_name": employee_name,
                    "position": position,
                    "department": department,
                    "salary": salary,
                    "phone": phone,
                    "email": email,
                    "hire_date": hire_date,
                    "created_at": self.db_manager.get_current_time(),
                    "is_active": True,
                    "total_leaves": 0,
                    "remaining_leaves": 30  # 30 يوم إجازة سنوية افتراضية
                }

                self.db_manager.employees_collection.insert_one(employee_data)

                # إضافة إشعار
                self.notification_manager.add_notification(
                    "موظف جديد",
                    f"تم إضافة الموظف {employee_name} بنجاح",
                    "success",
                    self.user_data.get('user_id')
                )

                messagebox.showinfo("نجح", "تم إضافة الموظف بنجاح")
                dialog.destroy()
                self.refresh_employees_list()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال راتب صحيح أو تاريخ صحيح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة الموظف: {str(e)}")

        save_button = ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_employee,
            width=100,
            height=35
        )
        save_button.pack(side="left", padx=10, pady=10)

        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=10, pady=10)

    def refresh_employees_list(self):
        """تحديث قائمة الموظفين"""
        try:
            # مسح البيانات السابقة
            for item in self.employees_tree.get_children():
                self.employees_tree.delete(item)

            # جلب الموظفين من قاعدة البيانات
            employees = list(self.db_manager.employees_collection.find().sort("created_at", -1))

            for employee in employees:
                hire_date = employee.get("hire_date", datetime.now()).strftime("%Y-%m-%d")
                salary = f"{employee.get('salary', 0):,.2f} ر.س"
                status = "نشط" if employee.get("is_active", True) else "غير نشط"

                self.employees_tree.insert("", "end", values=(
                    employee.get("employee_id", ""),
                    employee.get("employee_name", ""),
                    employee.get("position", ""),
                    employee.get("department", ""),
                    salary,
                    employee.get("phone", ""),
                    hire_date,
                    status
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث قائمة الموظفين: {str(e)}")

    def edit_employee_dialog(self, event):
        """نافذة تعديل موظف"""
        selection = self.employees_tree.selection()
        if not selection:
            return

        item = self.employees_tree.item(selection[0])
        employee_id = item['values'][0]

        # جلب بيانات الموظف من قاعدة البيانات
        employee = self.db_manager.employees_collection.find_one({"employee_id": employee_id})
        if not employee:
            messagebox.showerror("خطأ", "لم يتم العثور على الموظف")
            return

        messagebox.showinfo("تعديل الموظف", f"تعديل بيانات الموظف: {employee.get('employee_name', '')}")

    def manage_payroll(self):
        """إدارة الرواتب"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إدارة الرواتب")
        dialog.geometry("800x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="💰 إدارة الرواتب",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # إطار الشهر والسنة
        period_frame = ctk.CTkFrame(dialog)
        period_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(period_frame, text="الشهر:").pack(side="left", padx=10)
        month_var = ctk.StringVar(value=str(datetime.now().month))
        month_menu = ctk.CTkOptionMenu(
            period_frame,
            values=[str(i) for i in range(1, 13)],
            variable=month_var,
            width=100
        )
        month_menu.pack(side="left", padx=10)

        ctk.CTkLabel(period_frame, text="السنة:").pack(side="left", padx=10)
        year_var = ctk.StringVar(value=str(datetime.now().year))
        year_entry = ctk.CTkEntry(period_frame, textvariable=year_var, width=100)
        year_entry.pack(side="left", padx=10)

        generate_button = ctk.CTkButton(
            period_frame,
            text="إنشاء كشف الرواتب",
            command=lambda: self.generate_payroll(month_var.get(), year_var.get()),
            width=150
        )
        generate_button.pack(side="left", padx=20)

        # جدول الرواتب
        payroll_frame = ctk.CTkFrame(dialog)
        payroll_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ("رقم الموظف", "اسم الموظف", "الراتب الأساسي", "البدلات", "الخصومات", "صافي الراتب")
        payroll_tree = ttk.Treeview(payroll_frame, columns=columns, show="headings", height=15)

        for col in columns:
            payroll_tree.heading(col, text=col)
            payroll_tree.column(col, width=120)

        payroll_tree.pack(fill="both", expand=True, padx=10, pady=10)

        # تحميل بيانات الرواتب
        self.load_payroll_data(payroll_tree, month_var.get(), year_var.get())

    def manage_attendance(self):
        """إدارة الحضور"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إدارة الحضور")
        dialog.geometry("900x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="📅 إدارة الحضور",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(dialog)
        controls_frame.pack(fill="x", padx=20, pady=10)

        mark_attendance_button = ctk.CTkButton(
            controls_frame,
            text="✅ تسجيل حضور",
            command=self.mark_attendance,
            width=120
        )
        mark_attendance_button.pack(side="left", padx=10)

        view_report_button = ctk.CTkButton(
            controls_frame,
            text="📊 تقرير الحضور",
            command=self.view_attendance_report,
            width=120
        )
        view_report_button.pack(side="left", padx=10)

        # جدول الحضور
        attendance_frame = ctk.CTkFrame(dialog)
        attendance_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ("رقم الموظف", "اسم الموظف", "التاريخ", "وقت الدخول", "وقت الخروج", "ساعات العمل")
        attendance_tree = ttk.Treeview(attendance_frame, columns=columns, show="headings", height=15)

        for col in columns:
            attendance_tree.heading(col, text=col)
            attendance_tree.column(col, width=130)

        attendance_tree.pack(fill="both", expand=True, padx=10, pady=10)

        # تحميل بيانات الحضور
        self.load_attendance_data(attendance_tree)

    def generate_payroll(self, month, year):
        """إنشاء كشف الرواتب"""
        try:
            employees = list(self.db_manager.employees_collection.find({"is_active": True}))

            for employee in employees:
                payroll_data = {
                    "employee_id": employee["employee_id"],
                    "employee_name": employee["employee_name"],
                    "month": int(month),
                    "year": int(year),
                    "basic_salary": employee["salary"],
                    "allowances": 0.0,  # يمكن إضافة منطق للبدلات
                    "deductions": 0.0,  # يمكن إضافة منطق للخصومات
                    "net_salary": employee["salary"],
                    "created_at": self.db_manager.get_current_time(),
                    "status": "مدفوع"
                }

                # التحقق من عدم وجود راتب للشهر نفسه
                existing = self.db_manager.payroll_collection.find_one({
                    "employee_id": employee["employee_id"],
                    "month": int(month),
                    "year": int(year)
                })

                if not existing:
                    self.db_manager.payroll_collection.insert_one(payroll_data)

            messagebox.showinfo("نجح", f"تم إنشاء كشف الرواتب لشهر {month}/{year}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء كشف الرواتب: {str(e)}")

    def load_payroll_data(self, tree, month, year):
        """تحميل بيانات الرواتب"""
        try:
            # مسح البيانات السابقة
            for item in tree.get_children():
                tree.delete(item)

            payrolls = list(self.db_manager.payroll_collection.find({
                "month": int(month),
                "year": int(year)
            }))

            for payroll in payrolls:
                tree.insert("", "end", values=(
                    payroll.get("employee_id", ""),
                    payroll.get("employee_name", ""),
                    f"{payroll.get('basic_salary', 0):,.2f}",
                    f"{payroll.get('allowances', 0):,.2f}",
                    f"{payroll.get('deductions', 0):,.2f}",
                    f"{payroll.get('net_salary', 0):,.2f}"
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الرواتب: {str(e)}")

    def mark_attendance(self):
        """تسجيل حضور"""
        messagebox.showinfo("تسجيل الحضور", "تم تسجيل الحضور بنجاح")

    def view_attendance_report(self):
        """عرض تقرير الحضور"""
        messagebox.showinfo("تقرير الحضور", "عرض تقرير الحضور الشهري")

    def load_attendance_data(self, tree):
        """تحميل بيانات الحضور"""
        try:
            # مسح البيانات السابقة
            for item in tree.get_children():
                tree.delete(item)

            # بيانات تجريبية للحضور
            sample_data = [
                ("EMP001", "أحمد محمد", "2024-01-15", "08:00", "17:00", "9 ساعات"),
                ("EMP002", "فاطمة علي", "2024-01-15", "08:30", "17:30", "9 ساعات"),
            ]

            for data in sample_data:
                tree.insert("", "end", values=data)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الحضور: {str(e)}")

    def create_advanced_invoices_section(self):
        """إنشاء قسم الفواتير المتقدم"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="🧾 إدارة الفواتير المتقدمة",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        new_invoice_button = ctk.CTkButton(
            controls_frame,
            text="➕ فاتورة جديدة",
            command=self.create_new_invoice_dialog,
            width=120,
            height=35
        )
        new_invoice_button.pack(side="left", padx=10, pady=10)

        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_invoices_list,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)

        products_button = ctk.CTkButton(
            controls_frame,
            text="📦 إدارة المنتجات",
            command=self.manage_products,
            width=120,
            height=35
        )
        products_button.pack(side="left", padx=10, pady=10)

        # جدول الفواتير
        invoices_frame = ctk.CTkFrame(self.main_frame)
        invoices_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("رقم الفاتورة", "العميل", "التاريخ", "المبلغ الإجمالي", "الضريبة", "الصافي", "الحالة")

        self.invoices_tree = ttk.Treeview(invoices_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.invoices_tree.heading(col, text=col)
            self.invoices_tree.column(col, width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(invoices_frame, orient="vertical", command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.invoices_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # ربط النقر المزدوج لعرض تفاصيل الفاتورة
        self.invoices_tree.bind("<Double-1>", self.view_invoice_details)

        # تحديث قائمة الفواتير
        self.refresh_invoices_list()

    def create_new_invoice_dialog(self):
        """نافذة إنشاء فاتورة جديدة"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إنشاء فاتورة جديدة")
        dialog.geometry("900x700")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="إنشاء فاتورة جديدة",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 20))

        # معلومات الفاتورة الأساسية
        header_frame = ctk.CTkFrame(dialog)
        header_frame.pack(fill="x", padx=20, pady=10)

        # الصف الأول
        row1_frame = ctk.CTkFrame(header_frame)
        row1_frame.pack(fill="x", padx=10, pady=10)

        # رقم الفاتورة
        ctk.CTkLabel(row1_frame, text="رقم الفاتورة:").pack(side="left", padx=5)
        invoice_number_entry = ctk.CTkEntry(row1_frame, width=150)
        invoice_number_entry.insert(0, f"INV-{datetime.now().strftime('%Y%m%d%H%M%S')}")
        invoice_number_entry.pack(side="left", padx=5)

        # التاريخ
        ctk.CTkLabel(row1_frame, text="التاريخ:").pack(side="left", padx=(20, 5))
        date_entry = ctk.CTkEntry(row1_frame, width=150)
        date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        date_entry.pack(side="left", padx=5)

        # الصف الثاني
        row2_frame = ctk.CTkFrame(header_frame)
        row2_frame.pack(fill="x", padx=10, pady=10)

        # العميل
        ctk.CTkLabel(row2_frame, text="العميل:").pack(side="left", padx=5)

        # جلب قائمة العملاء
        customers = list(self.db_manager.customers_collection.find())
        customer_names = [f"{c['customer_code']} - {c['customer_name']}" for c in customers]

        customer_var = ctk.StringVar()
        customer_menu = ctk.CTkOptionMenu(
            row2_frame,
            values=customer_names if customer_names else ["لا توجد عملاء"],
            variable=customer_var,
            width=300
        )
        customer_menu.pack(side="left", padx=5)

        # أصناف الفاتورة
        items_frame = ctk.CTkFrame(dialog)
        items_frame.pack(fill="both", expand=True, padx=20, pady=10)

        ctk.CTkLabel(items_frame, text="أصناف الفاتورة", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # جدول الأصناف
        items_columns = ("المنتج", "الكمية", "السعر", "الخصم %", "المجموع")
        items_tree = ttk.Treeview(items_frame, columns=items_columns, show="headings", height=8)

        for col in items_columns:
            items_tree.heading(col, text=col)
            items_tree.column(col, width=120)

        items_tree.pack(fill="both", expand=True, padx=10, pady=10)

        # أزرار إدارة الأصناف
        items_controls = ctk.CTkFrame(items_frame)
        items_controls.pack(fill="x", padx=10, pady=5)

        add_item_button = ctk.CTkButton(
            items_controls,
            text="➕ إضافة صنف",
            command=lambda: self.add_invoice_item(items_tree),
            width=100
        )
        add_item_button.pack(side="left", padx=5)

        remove_item_button = ctk.CTkButton(
            items_controls,
            text="➖ حذف صنف",
            command=lambda: self.remove_invoice_item(items_tree),
            width=100
        )
        remove_item_button.pack(side="left", padx=5)

        # ملخص الفاتورة
        summary_frame = ctk.CTkFrame(dialog)
        summary_frame.pack(fill="x", padx=20, pady=10)

        # المجموع الفرعي
        subtotal_frame = ctk.CTkFrame(summary_frame)
        subtotal_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(subtotal_frame, text="المجموع الفرعي:").pack(side="left")
        subtotal_label = ctk.CTkLabel(subtotal_frame, text="0.00 ر.س")
        subtotal_label.pack(side="right")

        # الضريبة
        tax_frame = ctk.CTkFrame(summary_frame)
        tax_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(tax_frame, text="ضريبة القيمة المضافة (15%):").pack(side="left")
        tax_label = ctk.CTkLabel(tax_frame, text="0.00 ر.س")
        tax_label.pack(side="right")

        # المجموع النهائي
        total_frame = ctk.CTkFrame(summary_frame)
        total_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(total_frame, text="المجموع النهائي:", font=ctk.CTkFont(weight="bold")).pack(side="left")
        total_label = ctk.CTkLabel(total_frame, text="0.00 ر.س", font=ctk.CTkFont(weight="bold"))
        total_label.pack(side="right")

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        save_button = ctk.CTkButton(
            buttons_frame,
            text="حفظ الفاتورة",
            command=lambda: self.save_invoice(dialog, invoice_number_entry, customer_var, items_tree),
            width=120
        )
        save_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=10)

    def add_invoice_item(self, items_tree):
        """إضافة صنف للفاتورة"""
        # نافذة إضافة صنف
        item_dialog = ctk.CTkToplevel()
        item_dialog.title("إضافة صنف")
        item_dialog.geometry("400x300")
        item_dialog.transient()
        item_dialog.grab_set()

        # حقول الإدخال
        ctk.CTkLabel(item_dialog, text="المنتج:").pack(pady=5)
        product_entry = ctk.CTkEntry(item_dialog, width=300)
        product_entry.pack(pady=5)

        ctk.CTkLabel(item_dialog, text="الكمية:").pack(pady=5)
        quantity_entry = ctk.CTkEntry(item_dialog, width=300)
        quantity_entry.pack(pady=5)

        ctk.CTkLabel(item_dialog, text="السعر:").pack(pady=5)
        price_entry = ctk.CTkEntry(item_dialog, width=300)
        price_entry.pack(pady=5)

        ctk.CTkLabel(item_dialog, text="الخصم %:").pack(pady=5)
        discount_entry = ctk.CTkEntry(item_dialog, width=300)
        discount_entry.insert(0, "0")
        discount_entry.pack(pady=5)

        def add_item():
            try:
                product = product_entry.get().strip()
                quantity = float(quantity_entry.get())
                price = float(price_entry.get())
                discount = float(discount_entry.get())

                total = quantity * price * (1 - discount/100)

                items_tree.insert("", "end", values=(
                    product, quantity, f"{price:.2f}", f"{discount:.1f}", f"{total:.2f}"
                ))

                item_dialog.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة")

        ctk.CTkButton(item_dialog, text="إضافة", command=add_item).pack(pady=20)

    def remove_invoice_item(self, items_tree):
        """حذف صنف من الفاتورة"""
        selection = items_tree.selection()
        if selection:
            items_tree.delete(selection[0])

    def save_invoice(self, dialog, invoice_number_entry, customer_var, items_tree):
        """حفظ الفاتورة"""
        try:
            invoice_number = invoice_number_entry.get().strip()
            customer_info = customer_var.get()

            if not invoice_number or not customer_info:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return

            # استخراج كود العميل
            customer_code = customer_info.split(" - ")[0]

            # جمع أصناف الفاتورة
            items = []
            subtotal = 0

            for item in items_tree.get_children():
                values = items_tree.item(item)['values']
                item_total = float(values[4])
                subtotal += item_total

                items.append({
                    "product": values[0],
                    "quantity": float(values[1]),
                    "price": float(values[2]),
                    "discount": float(values[3]),
                    "total": item_total
                })

            if not items:
                messagebox.showerror("خطأ", "يرجى إضافة أصناف للفاتورة")
                return

            # حساب الضريبة والمجموع النهائي
            tax_rate = 0.15  # 15% ضريبة القيمة المضافة
            tax_amount = subtotal * tax_rate
            total_amount = subtotal + tax_amount

            # حفظ الفاتورة
            invoice_data = {
                "invoice_number": invoice_number,
                "customer_code": customer_code,
                "invoice_date": datetime.now(),
                "items": items,
                "subtotal": subtotal,
                "tax_amount": tax_amount,
                "total_amount": total_amount,
                "status": "مدفوعة",
                "created_at": self.db_manager.get_current_time(),
                "created_by": self.user_data.get('user_id')
            }

            # حفظ في قاعدة البيانات
            self.db_manager.invoices_collection.insert_one(invoice_data)

            # حفظ أصناف الفاتورة
            for item in items:
                item_data = {
                    "invoice_number": invoice_number,
                    "product": item["product"],
                    "quantity": item["quantity"],
                    "price": item["price"],
                    "discount": item["discount"],
                    "total": item["total"],
                    "created_at": self.db_manager.get_current_time()
                }
                self.db_manager.invoice_items_collection.insert_one(item_data)

            # إضافة إشعار
            self.notification_manager.add_notification(
                "فاتورة جديدة",
                f"تم إنشاء الفاتورة {invoice_number} بمبلغ {total_amount:.2f} ر.س",
                "success",
                self.user_data.get('user_id')
            )

            messagebox.showinfo("نجح", f"تم حفظ الفاتورة {invoice_number} بنجاح")
            dialog.destroy()
            self.refresh_invoices_list()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الفاتورة: {str(e)}")

    def refresh_invoices_list(self):
        """تحديث قائمة الفواتير"""
        try:
            # مسح البيانات السابقة
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)

            # جلب الفواتير من قاعدة البيانات
            invoices = list(self.db_manager.invoices_collection.find().sort("created_at", -1))

            for invoice in invoices:
                invoice_date = invoice.get("invoice_date", datetime.now()).strftime("%Y-%m-%d")

                # جلب اسم العميل
                customer = self.db_manager.customers_collection.find_one(
                    {"customer_code": invoice.get("customer_code")}
                )
                customer_name = customer.get("customer_name", "غير محدد") if customer else "غير محدد"

                self.invoices_tree.insert("", "end", values=(
                    invoice.get("invoice_number", ""),
                    customer_name,
                    invoice_date,
                    f"{invoice.get('subtotal', 0):,.2f}",
                    f"{invoice.get('tax_amount', 0):,.2f}",
                    f"{invoice.get('total_amount', 0):,.2f}",
                    invoice.get("status", "")
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث قائمة الفواتير: {str(e)}")

    def view_invoice_details(self, event):
        """عرض تفاصيل الفاتورة"""
        selection = self.invoices_tree.selection()
        if not selection:
            return

        item = self.invoices_tree.item(selection[0])
        invoice_number = item['values'][0]

        messagebox.showinfo("تفاصيل الفاتورة", f"عرض تفاصيل الفاتورة: {invoice_number}")

    def manage_products(self):
        """إدارة المنتجات"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إدارة المنتجات")
        dialog.geometry("700x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="📦 إدارة المنتجات",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 20))

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(dialog)
        controls_frame.pack(fill="x", padx=20, pady=10)

        add_product_button = ctk.CTkButton(
            controls_frame,
            text="➕ إضافة منتج",
            command=lambda: self.add_product_dialog(dialog),
            width=120
        )
        add_product_button.pack(side="left", padx=10)

        # جدول المنتجات
        products_frame = ctk.CTkFrame(dialog)
        products_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ("كود المنتج", "اسم المنتج", "السعر", "الكمية المتاحة", "الوحدة")
        products_tree = ttk.Treeview(products_frame, columns=columns, show="headings", height=15)

        for col in columns:
            products_tree.heading(col, text=col)
            products_tree.column(col, width=120)

        products_tree.pack(fill="both", expand=True, padx=10, pady=10)

        # تحميل بيانات المنتجات
        self.load_products_data(products_tree)

    def add_product_dialog(self, parent_dialog):
        """نافذة إضافة منتج"""
        messagebox.showinfo("إضافة منتج", "سيتم إضافة نافذة إضافة المنتجات قريباً")

    def load_products_data(self, tree):
        """تحميل بيانات المنتجات"""
        try:
            # مسح البيانات السابقة
            for item in tree.get_children():
                tree.delete(item)

            # بيانات تجريبية للمنتجات
            sample_products = [
                ("PROD001", "منتج تجريبي 1", "100.00", "50", "قطعة"),
                ("PROD002", "منتج تجريبي 2", "250.00", "25", "كيلو"),
            ]

            for product in sample_products:
                tree.insert("", "end", values=product)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المنتجات: {str(e)}")

    def create_reports_section(self):
        """إنشاء قسم التقارير"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📈 التقارير المالية",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # أنواع التقارير
        reports_frame = ctk.CTkFrame(self.main_frame)
        reports_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        reports_title = ctk.CTkLabel(
            reports_frame,
            text="أنواع التقارير المتاحة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        reports_title.pack(pady=(20, 20))
        
        # بطاقات التقارير
        reports_grid = ctk.CTkFrame(reports_frame)
        reports_grid.pack(fill="x", padx=20, pady=10)
        
        reports_grid.grid_columnconfigure((0, 1, 2), weight=1)
        
        # تقرير المعاملات الشهرية
        self.create_report_card(
            reports_grid, 
            "📊 تقرير المعاملات الشهرية",
            "عرض جميع المعاملات المالية للشهر الحالي",
            lambda: self.generate_report("monthly_transactions"),
            0, 0
        )
        
        # تقرير الموردين
        self.create_report_card(
            reports_grid,
            "🏪 تقرير الموردين",
            "عرض تفاصيل جميع الموردين ومعاملاتهم",
            lambda: self.generate_report("suppliers"),
            0, 1
        )
        
        # تقرير الفواتير
        self.create_report_card(
            reports_grid,
            "🧾 تقرير الفواتير",
            "عرض حالة جميع الفواتير والمدفوعات",
            lambda: self.generate_report("invoices"),
            0, 2
        )
    
    def create_report_card(self, parent, title, description, command, row, col):
        """إنشاء بطاقة تقرير"""
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # عنوان التقرير
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(pady=(15, 10))
        
        # وصف التقرير
        desc_label = ctk.CTkLabel(
            card_frame,
            text=description,
            font=ctk.CTkFont(size=11),
            wraplength=200
        )
        desc_label.pack(pady=(0, 15))
        
        # زر إنشاء التقرير
        generate_button = ctk.CTkButton(
            card_frame,
            text="إنشاء التقرير",
            command=command,
            width=150,
            height=35
        )
        generate_button.pack(pady=(0, 15))
    
    def generate_report(self, report_type):
        """إنشاء تقرير متقدم"""
        self.show_report_generation_dialog(report_type)

    def create_admin_transactions_section(self):
        """إنشاء قسم إدارة المعاملات للمدير"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="💰 إدارة المعاملات المالية",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # إحصائيات سريعة
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)

        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # بطاقات الإحصائيات
        self.create_stat_card(stats_frame, "💳", "إجمالي المعاملات", "156", 0, 0)
        self.create_stat_card(stats_frame, "📈", "الإيرادات", "125,000 ر.س", 0, 1)
        self.create_stat_card(stats_frame, "📉", "المصروفات", "45,000 ر.س", 0, 2)
        self.create_stat_card(stats_frame, "💰", "الصافي", "80,000 ر.س", 0, 3)

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        add_transaction_button = ctk.CTkButton(
            controls_frame,
            text="➕ إضافة معاملة جديدة",
            command=self.add_admin_transaction_dialog,
            width=150,
            height=35
        )
        add_transaction_button.pack(side="left", padx=10, pady=10)

        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_admin_transactions_list,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)

        export_button = ctk.CTkButton(
            controls_frame,
            text="📊 تصدير تقرير",
            command=self.export_transactions_report,
            width=120,
            height=35
        )
        export_button.pack(side="left", padx=10, pady=10)

        # جدول المعاملات
        transactions_frame = ctk.CTkFrame(self.main_frame)
        transactions_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("التاريخ", "النوع", "الوصف", "المبلغ", "الحساب", "المستخدم", "الحالة")

        self.admin_transactions_tree = ttk.Treeview(transactions_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.admin_transactions_tree.heading(col, text=col)
            self.admin_transactions_tree.column(col, width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(transactions_frame, orient="vertical", command=self.admin_transactions_tree.yview)
        self.admin_transactions_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.admin_transactions_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # تحديث قائمة المعاملات
        self.refresh_admin_transactions_list()

    def add_admin_transaction_dialog(self):
        """نافذة إضافة معاملة جديدة للمدير"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إضافة معاملة جديدة")
        dialog.geometry("450x650")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="إضافة معاملة مالية جديدة",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="x", padx=20, pady=10)

        # نوع المعاملة
        ctk.CTkLabel(fields_frame, text="نوع المعاملة:").pack(pady=(10, 5))
        transaction_type_var = ctk.StringVar(value="إيراد")
        transaction_type_menu = ctk.CTkOptionMenu(
            fields_frame,
            values=["إيراد", "مصروف", "تحويل"],
            variable=transaction_type_var,
            width=300
        )
        transaction_type_menu.pack(pady=(0, 10))

        # الوصف
        ctk.CTkLabel(fields_frame, text="وصف المعاملة:").pack(pady=(10, 5))
        description_entry = ctk.CTkEntry(fields_frame, width=300)
        description_entry.pack(pady=(0, 10))

        # المبلغ
        ctk.CTkLabel(fields_frame, text="المبلغ (ريال سعودي):").pack(pady=(10, 5))
        amount_entry = ctk.CTkEntry(fields_frame, width=300)
        amount_entry.pack(pady=(0, 10))

        # الحساب
        ctk.CTkLabel(fields_frame, text="الحساب:").pack(pady=(10, 5))
        account_var = ctk.StringVar(value="الحساب الرئيسي")
        account_menu = ctk.CTkOptionMenu(
            fields_frame,
            values=["الحساب الرئيسي", "حساب البنك", "الصندوق", "حساب العملاء", "حساب الموردين"],
            variable=account_var,
            width=300
        )
        account_menu.pack(pady=(0, 10))

        # التاريخ
        ctk.CTkLabel(fields_frame, text="التاريخ (YYYY-MM-DD):").pack(pady=(10, 5))
        date_entry = ctk.CTkEntry(fields_frame, width=300)
        date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        date_entry.pack(pady=(0, 10))

        # ملاحظات
        ctk.CTkLabel(fields_frame, text="ملاحظات:").pack(pady=(10, 5))
        notes_textbox = ctk.CTkTextbox(fields_frame, width=300, height=80)
        notes_textbox.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        def save_transaction():
            # التحقق من البيانات
            transaction_type = transaction_type_var.get()
            description = description_entry.get().strip()
            amount_str = amount_entry.get().strip()
            account = account_var.get()
            date_str = date_entry.get().strip()
            notes = notes_textbox.get("1.0", "end-1c").strip()

            if not all([description, amount_str, date_str]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return

            try:
                amount = float(amount_str)
                transaction_date = datetime.strptime(date_str, "%Y-%m-%d")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى التحقق من صحة المبلغ والتاريخ")
                return

            try:
                # إنشاء المعاملة
                transaction_data = {
                    "type": transaction_type,
                    "description": description,
                    "amount": amount,
                    "account": account,
                    "date": transaction_date,
                    "notes": notes,
                    "created_by": self.user_data["username"],
                    "created_at": datetime.now(),
                    "status": "مكتملة"
                }

                # حفظ في قاعدة البيانات
                self.db_manager.transactions_collection.insert_one(transaction_data)

                # إضافة إشعار
                self.notification_manager.add_notification(
                    "معاملة جديدة",
                    f"تم إضافة معاملة {transaction_type} بقيمة {amount:,.2f} ر.س",
                    "success",
                    self.user_data.get('_id')
                )

                messagebox.showinfo("نجح", "تم إضافة المعاملة بنجاح")
                dialog.destroy()
                self.refresh_admin_transactions_list()
                self.update_notifications_count()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ المعاملة: {str(e)}")

        save_button = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=save_transaction,
            width=100,
            height=35
        )
        save_button.pack(side="right", padx=10, pady=10)

        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            width=100,
            height=35
        )
        cancel_button.pack(side="left", padx=10, pady=10)

    def refresh_admin_transactions_list(self):
        """تحديث قائمة المعاملات للمدير"""
        try:
            # مسح البيانات السابقة
            for item in self.admin_transactions_tree.get_children():
                self.admin_transactions_tree.delete(item)

            # جلب المعاملات من قاعدة البيانات
            transactions = list(self.db_manager.transactions_collection.find().sort("created_at", -1).limit(100))

            for transaction in transactions:
                # تنسيق البيانات للعرض
                date_str = transaction.get("date", datetime.now()).strftime("%Y-%m-%d")
                transaction_type = transaction.get("type", "غير محدد")
                description = transaction.get("description", "")
                amount = f"{transaction.get('amount', 0):,.2f} ر.س"
                account = transaction.get("account", "غير محدد")
                created_by = transaction.get("created_by", "غير معروف")
                status = transaction.get("status", "مكتملة")

                # إضافة إلى الجدول
                self.admin_transactions_tree.insert("", "end", values=(
                    date_str, transaction_type, description, amount, account, created_by, status
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث قائمة المعاملات: {str(e)}")

    def export_transactions_report(self):
        """تصدير تقرير المعاملات"""
        messagebox.showinfo("قريباً", "سيتم إضافة ميزة تصدير التقارير قريباً")

    def create_suppliers_management(self):
        """إنشاء قسم إدارة الموردين"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="🏪 إدارة الموردين",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # إحصائيات سريعة
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)

        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # بطاقات الإحصائيات
        self.create_stat_card(stats_frame, "🏪", "إجمالي الموردين", "25", 0, 0)
        self.create_stat_card(stats_frame, "✅", "نشط", "20", 0, 1)
        self.create_stat_card(stats_frame, "⏸️", "معلق", "3", 0, 2)
        self.create_stat_card(stats_frame, "❌", "محظور", "2", 0, 3)

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        add_supplier_button = ctk.CTkButton(
            controls_frame,
            text="➕ إضافة مورد جديد",
            command=self.add_supplier_dialog,
            width=150,
            height=35
        )
        add_supplier_button.pack(side="left", padx=10, pady=10)

        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_suppliers_list,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)

        # جدول الموردين
        suppliers_frame = ctk.CTkFrame(self.main_frame)
        suppliers_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("اسم المورد", "رقم الهاتف", "البريد الإلكتروني", "العنوان", "الحالة", "تاريخ التسجيل")

        self.suppliers_tree = ttk.Treeview(suppliers_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.suppliers_tree.heading(col, text=col)
            self.suppliers_tree.column(col, width=150)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(suppliers_frame, orient="vertical", command=self.suppliers_tree.yview)
        self.suppliers_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.suppliers_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # تحديث قائمة الموردين
        self.refresh_suppliers_list()

    def add_supplier_dialog(self):
        """نافذة إضافة مورد جديد"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إضافة مورد جديد")
        dialog.geometry("400x550")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="إضافة مورد جديد",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="x", padx=20, pady=10)

        # اسم المورد
        ctk.CTkLabel(fields_frame, text="اسم المورد:").pack(pady=(10, 5))
        supplier_name_entry = ctk.CTkEntry(fields_frame, width=300)
        supplier_name_entry.pack(pady=(0, 10))

        # رقم الهاتف
        ctk.CTkLabel(fields_frame, text="رقم الهاتف:").pack(pady=(10, 5))
        phone_entry = ctk.CTkEntry(fields_frame, width=300)
        phone_entry.pack(pady=(0, 10))

        # البريد الإلكتروني
        ctk.CTkLabel(fields_frame, text="البريد الإلكتروني:").pack(pady=(10, 5))
        email_entry = ctk.CTkEntry(fields_frame, width=300)
        email_entry.pack(pady=(0, 10))

        # العنوان
        ctk.CTkLabel(fields_frame, text="العنوان:").pack(pady=(10, 5))
        address_entry = ctk.CTkEntry(fields_frame, width=300)
        address_entry.pack(pady=(0, 10))

        # نوع النشاط
        ctk.CTkLabel(fields_frame, text="نوع النشاط:").pack(pady=(10, 5))
        activity_entry = ctk.CTkEntry(fields_frame, width=300)
        activity_entry.pack(pady=(0, 10))

        # الحالة
        ctk.CTkLabel(fields_frame, text="الحالة:").pack(pady=(10, 5))
        status_var = ctk.StringVar(value="نشط")
        status_menu = ctk.CTkOptionMenu(
            fields_frame,
            values=["نشط", "معلق", "محظور"],
            variable=status_var,
            width=300
        )
        status_menu.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        def save_supplier():
            # التحقق من البيانات
            supplier_name = supplier_name_entry.get().strip()
            phone = phone_entry.get().strip()
            email = email_entry.get().strip()
            address = address_entry.get().strip()
            activity = activity_entry.get().strip()
            status = status_var.get()

            if not all([supplier_name, phone]):
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة على الأقل (الاسم والهاتف)")
                return

            try:
                # إنشاء المورد
                supplier_data = {
                    "name": supplier_name,
                    "phone": phone,
                    "email": email,
                    "address": address,
                    "activity_type": activity,
                    "status": status,
                    "created_by": self.user_data["username"],
                    "created_at": datetime.now()
                }

                # حفظ في قاعدة البيانات
                self.db_manager.suppliers_collection.insert_one(supplier_data)

                # إضافة إشعار
                self.notification_manager.add_notification(
                    "مورد جديد",
                    f"تم تسجيل مورد جديد: {supplier_name}",
                    "info",
                    self.user_data.get('_id')
                )

                messagebox.showinfo("نجح", "تم إضافة المورد بنجاح")
                dialog.destroy()
                self.refresh_suppliers_list()
                self.update_notifications_count()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ المورد: {str(e)}")

        save_button = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=save_supplier,
            width=100,
            height=35
        )
        save_button.pack(side="right", padx=10, pady=10)

        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            width=100,
            height=35
        )
        cancel_button.pack(side="left", padx=10, pady=10)

    def refresh_suppliers_list(self):
        """تحديث قائمة الموردين"""
        try:
            # مسح البيانات السابقة
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            # جلب الموردين من قاعدة البيانات
            suppliers = list(self.db_manager.suppliers_collection.find().sort("created_at", -1))

            for supplier in suppliers:
                # تنسيق البيانات للعرض
                name = supplier.get("name", "غير محدد")
                phone = supplier.get("phone", "غير محدد")
                email = supplier.get("email", "غير محدد")
                address = supplier.get("address", "غير محدد")
                status = supplier.get("status", "نشط")
                created_at = supplier.get("created_at", datetime.now()).strftime("%Y-%m-%d")

                # إضافة إلى الجدول
                self.suppliers_tree.insert("", "end", values=(
                    name, phone, email, address, status, created_at
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث قائمة الموردين: {str(e)}")

    def create_admin_invoices_section(self):
        """إنشاء قسم إدارة الفواتير للمدير"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="🧾 إدارة الفواتير",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # إحصائيات سريعة
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)

        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # بطاقات الإحصائيات
        self.create_stat_card(stats_frame, "🧾", "إجمالي الفواتير", "89", 0, 0)
        self.create_stat_card(stats_frame, "✅", "مدفوعة", "65", 0, 1)
        self.create_stat_card(stats_frame, "⏳", "معلقة", "20", 0, 2)
        self.create_stat_card(stats_frame, "❌", "ملغاة", "4", 0, 3)

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_admin_invoices_list,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)

        export_button = ctk.CTkButton(
            controls_frame,
            text="📊 تصدير تقرير",
            command=self.export_invoices_report,
            width=120,
            height=35
        )
        export_button.pack(side="left", padx=10, pady=10)

        # جدول الفواتير
        invoices_frame = ctk.CTkFrame(self.main_frame)
        invoices_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("رقم الفاتورة", "المورد", "التاريخ", "المبلغ", "الحالة", "تاريخ الاستحقاق")

        self.admin_invoices_tree = ttk.Treeview(invoices_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.admin_invoices_tree.heading(col, text=col)
            self.admin_invoices_tree.column(col, width=150)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(invoices_frame, orient="vertical", command=self.admin_invoices_tree.yview)
        self.admin_invoices_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.admin_invoices_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # تحديث قائمة الفواتير
        self.refresh_admin_invoices_list()

    def refresh_admin_invoices_list(self):
        """تحديث قائمة الفواتير للمدير"""
        try:
            # مسح البيانات السابقة
            for item in self.admin_invoices_tree.get_children():
                self.admin_invoices_tree.delete(item)

            # جلب الفواتير من قاعدة البيانات
            invoices = list(self.db_manager.invoices_collection.find().sort("created_at", -1))

            for invoice in invoices:
                # تنسيق البيانات للعرض
                invoice_number = invoice.get("invoice_number", "غير محدد")
                supplier = invoice.get("supplier_name", "غير محدد")
                date = invoice.get("date", datetime.now()).strftime("%Y-%m-%d")
                amount = f"{invoice.get('total_amount', 0):,.2f} ر.س"
                status = invoice.get("status", "معلقة")
                due_date = invoice.get("due_date", datetime.now()).strftime("%Y-%m-%d")

                # إضافة إلى الجدول
                self.admin_invoices_tree.insert("", "end", values=(
                    invoice_number, supplier, date, amount, status, due_date
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث قائمة الفواتير: {str(e)}")

    def export_invoices_report(self):
        """تصدير تقرير الفواتير"""
        messagebox.showinfo("قريباً", "سيتم إضافة ميزة تصدير تقارير الفواتير قريباً")

    def create_admin_payments_section(self):
        """إنشاء قسم إدارة المدفوعات للمدير"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="💳 إدارة المدفوعات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # إحصائيات سريعة
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)

        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # بطاقات الإحصائيات
        self.create_stat_card(stats_frame, "💳", "إجمالي المدفوعات", "156", 0, 0)
        self.create_stat_card(stats_frame, "✅", "مكتملة", "140", 0, 1)
        self.create_stat_card(stats_frame, "⏳", "معلقة", "12", 0, 2)
        self.create_stat_card(stats_frame, "❌", "فاشلة", "4", 0, 3)

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_admin_payments_list,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)

        # جدول المدفوعات
        payments_frame = ctk.CTkFrame(self.main_frame)
        payments_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("رقم المدفوعة", "المورد", "التاريخ", "المبلغ", "طريقة الدفع", "الحالة")

        self.admin_payments_tree = ttk.Treeview(payments_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.admin_payments_tree.heading(col, text=col)
            self.admin_payments_tree.column(col, width=150)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(payments_frame, orient="vertical", command=self.admin_payments_tree.yview)
        self.admin_payments_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.admin_payments_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # تحديث قائمة المدفوعات
        self.refresh_admin_payments_list()

    def refresh_admin_payments_list(self):
        """تحديث قائمة المدفوعات للمدير"""
        try:
            # مسح البيانات السابقة
            for item in self.admin_payments_tree.get_children():
                self.admin_payments_tree.delete(item)

            # بيانات تجريبية للمدفوعات
            sample_payments = [
                ("PAY001", "شركة الإمدادات المتقدمة", "2024-01-15", "15,000.00 ر.س", "تحويل بنكي", "مكتملة"),
                ("PAY002", "مؤسسة التقنية الحديثة", "2024-01-14", "8,500.00 ر.س", "شيك", "مكتملة"),
                ("PAY003", "شركة المواد الأساسية", "2024-01-13", "12,000.00 ر.س", "نقد", "معلقة"),
                ("PAY004", "مجموعة الخدمات المتكاملة", "2024-01-12", "25,000.00 ر.س", "تحويل بنكي", "مكتملة"),
                ("PAY005", "شركة التوريدات السريعة", "2024-01-11", "6,750.00 ر.س", "بطاقة ائتمان", "فاشلة")
            ]

            for payment in sample_payments:
                self.admin_payments_tree.insert("", "end", values=payment)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث قائمة المدفوعات: {str(e)}")

    def create_admin_settings_section(self):
        """إنشاء قسم إعدادات النظام للمدير"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="🔧 إعدادات النظام",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # إعدادات عامة
        general_frame = ctk.CTkFrame(self.main_frame)
        general_frame.pack(fill="x", padx=20, pady=10)

        general_title = ctk.CTkLabel(
            general_frame,
            text="⚙️ الإعدادات العامة",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        general_title.pack(pady=(15, 10))

        # اسم الشركة
        company_frame = ctk.CTkFrame(general_frame)
        company_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(company_frame, text="اسم الشركة:").pack(side="left", padx=10, pady=10)
        company_entry = ctk.CTkEntry(company_frame, width=300)
        company_entry.insert(0, "شركة المحاسبة المتقدمة")
        company_entry.pack(side="right", padx=10, pady=10)

        # عنوان الشركة
        address_frame = ctk.CTkFrame(general_frame)
        address_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(address_frame, text="عنوان الشركة:").pack(side="left", padx=10, pady=10)
        address_entry = ctk.CTkEntry(address_frame, width=300)
        address_entry.insert(0, "الرياض، المملكة العربية السعودية")
        address_entry.pack(side="right", padx=10, pady=10)

        # رقم الهاتف
        phone_frame = ctk.CTkFrame(general_frame)
        phone_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(phone_frame, text="رقم الهاتف:").pack(side="left", padx=10, pady=10)
        phone_entry = ctk.CTkEntry(phone_frame, width=300)
        phone_entry.insert(0, "+966 11 123 4567")
        phone_entry.pack(side="right", padx=10, pady=10)

        # البريد الإلكتروني
        email_frame = ctk.CTkFrame(general_frame)
        email_frame.pack(fill="x", padx=20, pady=(5, 15))

        ctk.CTkLabel(email_frame, text="البريد الإلكتروني:").pack(side="left", padx=10, pady=10)
        email_entry = ctk.CTkEntry(email_frame, width=300)
        email_entry.insert(0, "<EMAIL>")
        email_entry.pack(side="right", padx=10, pady=10)

        # إعدادات النظام
        system_frame = ctk.CTkFrame(self.main_frame)
        system_frame.pack(fill="x", padx=20, pady=10)

        system_title = ctk.CTkLabel(
            system_frame,
            text="🖥️ إعدادات النظام",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        system_title.pack(pady=(15, 10))

        # العملة الافتراضية
        currency_frame = ctk.CTkFrame(system_frame)
        currency_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(currency_frame, text="العملة الافتراضية:").pack(side="left", padx=10, pady=10)
        currency_var = ctk.StringVar(value="ريال سعودي")
        currency_menu = ctk.CTkOptionMenu(
            currency_frame,
            values=["ريال سعودي", "دولار أمريكي", "يورو", "درهم إماراتي"],
            variable=currency_var,
            width=200
        )
        currency_menu.pack(side="right", padx=10, pady=10)

        # اللغة
        language_frame = ctk.CTkFrame(system_frame)
        language_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(language_frame, text="لغة النظام:").pack(side="left", padx=10, pady=10)
        language_var = ctk.StringVar(value="العربية")
        language_menu = ctk.CTkOptionMenu(
            language_frame,
            values=["العربية", "English"],
            variable=language_var,
            width=200
        )
        language_menu.pack(side="right", padx=10, pady=10)

        # المنطقة الزمنية
        timezone_frame = ctk.CTkFrame(system_frame)
        timezone_frame.pack(fill="x", padx=20, pady=(5, 15))

        ctk.CTkLabel(timezone_frame, text="المنطقة الزمنية:").pack(side="left", padx=10, pady=10)
        timezone_var = ctk.StringVar(value="توقيت الرياض")
        timezone_menu = ctk.CTkOptionMenu(
            timezone_frame,
            values=["توقيت الرياض", "توقيت مكة", "توقيت جدة"],
            variable=timezone_var,
            width=200
        )
        timezone_menu.pack(side="right", padx=10, pady=10)

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(fill="x", padx=20, pady=20)

        def save_settings():
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")

        def reset_settings():
            if messagebox.askyesno("تأكيد", "هل أنت متأكد من إعادة تعيين جميع الإعدادات؟"):
                messagebox.showinfo("تم", "تم إعادة تعيين الإعدادات")

        save_button = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ الإعدادات",
            command=save_settings,
            width=150,
            height=40
        )
        save_button.pack(side="left", padx=10, pady=10)

        reset_button = ctk.CTkButton(
            buttons_frame,
            text="🔄 إعادة تعيين",
            command=reset_settings,
            width=150,
            height=40
        )
        reset_button.pack(side="left", padx=10, pady=10)

        backup_button = ctk.CTkButton(
            buttons_frame,
            text="💾 نسخ احتياطي",
            command=self.create_backup,
            width=150,
            height=40
        )
        backup_button.pack(side="right", padx=10, pady=10)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        messagebox.showinfo("قريباً", "سيتم إضافة ميزة النسخ الاحتياطي قريباً")

    def show_report_generation_dialog(self, report_type):
        """عرض نافذة إنشاء التقارير المتقدمة"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إنشاء تقرير متقدم")
        dialog.geometry("500x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text=f"إنشاء تقرير: {self.get_report_title(report_type)}",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # إعدادات التقرير
        settings_frame = ctk.CTkFrame(dialog)
        settings_frame.pack(fill="x", padx=20, pady=10)

        # نوع التصدير
        ctk.CTkLabel(settings_frame, text="نوع التصدير:").pack(pady=(15, 5))
        export_type_var = ctk.StringVar(value="PDF")
        export_type_frame = ctk.CTkFrame(settings_frame)
        export_type_frame.pack(fill="x", padx=20, pady=5)

        pdf_radio = ctk.CTkRadioButton(export_type_frame, text="PDF", variable=export_type_var, value="PDF")
        pdf_radio.pack(side="left", padx=20, pady=10)

        excel_radio = ctk.CTkRadioButton(export_type_frame, text="Excel", variable=export_type_var, value="Excel")
        excel_radio.pack(side="left", padx=20, pady=10)

        # فترة التقرير
        ctk.CTkLabel(settings_frame, text="فترة التقرير:").pack(pady=(15, 5))
        period_var = ctk.StringVar(value="الشهر الحالي")
        period_menu = ctk.CTkOptionMenu(
            settings_frame,
            values=["اليوم الحالي", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة مخصصة"],
            variable=period_var,
            width=300
        )
        period_menu.pack(pady=(0, 10))

        # التاريخ المخصص (يظهر عند اختيار فترة مخصصة)
        custom_date_frame = ctk.CTkFrame(settings_frame)
        custom_date_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(custom_date_frame, text="من تاريخ:").pack(side="left", padx=10, pady=10)
        from_date_entry = ctk.CTkEntry(custom_date_frame, width=120)
        from_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        from_date_entry.pack(side="left", padx=5, pady=10)

        ctk.CTkLabel(custom_date_frame, text="إلى تاريخ:").pack(side="left", padx=10, pady=10)
        to_date_entry = ctk.CTkEntry(custom_date_frame, width=120)
        to_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        to_date_entry.pack(side="left", padx=5, pady=10)

        # خيارات إضافية
        options_frame = ctk.CTkFrame(settings_frame)
        options_frame.pack(fill="x", padx=20, pady=(15, 20))

        include_charts_var = ctk.BooleanVar(value=True)
        include_charts_check = ctk.CTkCheckBox(
            options_frame,
            text="تضمين الرسوم البيانية",
            variable=include_charts_var
        )
        include_charts_check.pack(anchor="w", padx=20, pady=5)

        include_summary_var = ctk.BooleanVar(value=True)
        include_summary_check = ctk.CTkCheckBox(
            options_frame,
            text="تضمين الملخص التنفيذي",
            variable=include_summary_var
        )
        include_summary_check.pack(anchor="w", padx=20, pady=5)

        detailed_view_var = ctk.BooleanVar(value=False)
        detailed_view_check = ctk.CTkCheckBox(
            options_frame,
            text="عرض تفصيلي",
            variable=detailed_view_var
        )
        detailed_view_check.pack(anchor="w", padx=20, pady=5)

        # معاينة التقرير
        preview_frame = ctk.CTkFrame(dialog)
        preview_frame.pack(fill="both", expand=True, padx=20, pady=10)

        preview_title = ctk.CTkLabel(
            preview_frame,
            text="معاينة التقرير",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        preview_title.pack(pady=(15, 10))

        preview_text = ctk.CTkTextbox(preview_frame, height=150)
        preview_text.pack(fill="both", expand=True, padx=20, pady=(0, 15))

        # إدراج نص المعاينة
        preview_content = self.generate_report_preview(report_type)
        preview_text.insert("1.0", preview_content)
        preview_text.configure(state="disabled")

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=20)

        def generate_and_export():
            export_type = export_type_var.get()
            period = period_var.get()
            include_charts = include_charts_var.get()
            include_summary = include_summary_var.get()
            detailed_view = detailed_view_var.get()

            # محاكاة إنشاء التقرير
            progress_dialog = self.show_progress_dialog("جاري إنشاء التقرير...")

            # محاكاة وقت المعالجة
            self.root.after(2000, lambda: self.complete_report_generation(
                progress_dialog, export_type, report_type, dialog
            ))

        generate_button = ctk.CTkButton(
            buttons_frame,
            text="📊 إنشاء وتصدير التقرير",
            command=generate_and_export,
            width=200,
            height=40
        )
        generate_button.pack(side="right", padx=10, pady=10)

        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            width=100,
            height=40
        )
        cancel_button.pack(side="left", padx=10, pady=10)

    def get_report_title(self, report_type):
        """الحصول على عنوان التقرير"""
        titles = {
            "monthly_transactions": "تقرير المعاملات الشهرية",
            "suppliers": "تقرير الموردين",
            "invoices": "تقرير الفواتير",
            "financial_summary": "الملخص المالي",
            "daily_transactions": "تقرير المعاملات اليومية",
            "balances": "تقرير الأرصدة"
        }
        return titles.get(report_type, "تقرير عام")

    def generate_report_preview(self, report_type):
        """إنشاء معاينة التقرير"""
        current_date = datetime.now().strftime("%Y-%m-%d")

        if report_type == "monthly_transactions":
            return f"""تقرير المعاملات الشهرية - {current_date}

📊 ملخص المعاملات:
• إجمالي المعاملات: 156 معاملة
• إجمالي الإيرادات: 125,000 ر.س
• إجمالي المصروفات: 45,000 ر.س
• صافي الربح: 80,000 ر.س

📈 أعلى المعاملات:
• معاملة رقم #001: 15,000 ر.س
• معاملة رقم #045: 12,500 ر.س
• معاملة رقم #089: 10,000 ر.س

📉 التوزيع حسب النوع:
• مبيعات: 60%
• مشتريات: 25%
• مصروفات إدارية: 15%"""

        elif report_type == "suppliers":
            return f"""تقرير الموردين - {current_date}

🏪 إحصائيات الموردين:
• إجمالي الموردين: 25 مورد
• موردين نشطين: 20 مورد
• موردين معلقين: 3 موردين
• موردين محظورين: 2 مورد

💰 أعلى الموردين قيمة:
• شركة الإمدادات المتقدمة: 45,000 ر.س
• مؤسسة التقنية الحديثة: 32,000 ر.س
• شركة المواد الأساسية: 28,000 ر.س

📊 التوزيع الجغرافي:
• الرياض: 40%
• جدة: 30%
• الدمام: 20%
• أخرى: 10%"""

        else:
            return f"""تقرير {self.get_report_title(report_type)} - {current_date}

📋 معلومات عامة:
• تاريخ التقرير: {current_date}
• فترة التقرير: الشهر الحالي
• نوع التقرير: {self.get_report_title(report_type)}

📊 البيانات الأساسية:
• سيتم عرض البيانات التفصيلية هنا
• مع الرسوم البيانية والإحصائيات
• والتحليلات المطلوبة

💡 ملاحظات:
• هذه معاينة أولية للتقرير
• البيانات الفعلية ستظهر عند الإنشاء"""

    def show_progress_dialog(self, message):
        """عرض نافذة التقدم"""
        progress_dialog = ctk.CTkToplevel(self.root)
        progress_dialog.title("جاري المعالجة...")
        progress_dialog.geometry("300x150")
        progress_dialog.transient(self.root)
        progress_dialog.grab_set()

        # رسالة التقدم
        message_label = ctk.CTkLabel(
            progress_dialog,
            text=message,
            font=ctk.CTkFont(size=14)
        )
        message_label.pack(pady=(30, 20))

        # شريط التقدم
        progress_bar = ctk.CTkProgressBar(progress_dialog, width=250)
        progress_bar.pack(pady=10)
        progress_bar.set(0.5)  # محاكاة التقدم

        return progress_dialog

    def complete_report_generation(self, progress_dialog, export_type, report_type, parent_dialog):
        """إكمال إنشاء التقرير"""
        progress_dialog.destroy()

        # محاكاة حفظ الملف
        filename = f"تقرير_{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{export_type.lower()}"

        messagebox.showinfo(
            "تم بنجاح",
            f"تم إنشاء التقرير بنجاح!\n\nاسم الملف: {filename}\nنوع الملف: {export_type}\n\nتم حفظ التقرير في مجلد التقارير."
        )

        parent_dialog.destroy()

    def create_placeholder_section(self, section_name):
        """إنشاء قسم مؤقت"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # رسالة مؤقتة
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text=f"قسم {section_name} قيد التطوير",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        placeholder_label.pack(expand=True)
    
    def show_notifications(self):
        """عرض نافذة الإشعارات"""
        self.show_notifications_window()

    def show_notifications_window(self):
        """عرض نافذة الإشعارات المتقدمة"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("مركز الإشعارات")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="🔔 مركز الإشعارات",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(20, 10))

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(dialog)
        controls_frame.pack(fill="x", padx=20, pady=10)

        mark_all_read_button = ctk.CTkButton(
            controls_frame,
            text="✅ تمييز الكل كمقروء",
            command=self.mark_all_notifications_read,
            width=150,
            height=35
        )
        mark_all_read_button.pack(side="left", padx=10, pady=10)

        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=lambda: self.refresh_notifications_list(notifications_frame),
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)

        # قائمة الإشعارات
        notifications_frame = ctk.CTkScrollableFrame(dialog, height=300)
        notifications_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # تحميل الإشعارات
        self.load_notifications(notifications_frame)

        # زر الإغلاق
        close_button = ctk.CTkButton(
            dialog,
            text="❌ إغلاق",
            command=dialog.destroy,
            width=100,
            height=35
        )
        close_button.pack(pady=20)

    def load_notifications(self, parent_frame):
        """تحميل الإشعارات"""
        # مسح الإشعارات السابقة
        for widget in parent_frame.winfo_children():
            widget.destroy()

        # إشعارات تجريبية
        sample_notifications = [
            {
                "id": "1",
                "title": "معاملة جديدة",
                "message": "تم إضافة معاملة جديدة بقيمة 15,000 ر.س",
                "type": "success",
                "created_at": datetime.now() - timedelta(minutes=5),
                "read": False
            },
            {
                "id": "2",
                "title": "فاتورة مستحقة",
                "message": "فاتورة رقم INV-001 مستحقة الدفع خلال 3 أيام",
                "type": "warning",
                "created_at": datetime.now() - timedelta(hours=2),
                "read": False
            },
            {
                "id": "3",
                "title": "مورد جديد",
                "message": "تم تسجيل مورد جديد: شركة التقنية المتقدمة",
                "type": "info",
                "created_at": datetime.now() - timedelta(hours=5),
                "read": True
            },
            {
                "id": "4",
                "title": "تقرير شهري",
                "message": "تم إنشاء التقرير الشهري بنجاح",
                "type": "success",
                "created_at": datetime.now() - timedelta(days=1),
                "read": True
            },
            {
                "id": "5",
                "title": "خطأ في النظام",
                "message": "فشل في الاتصال بقاعدة البيانات مؤقتاً",
                "type": "error",
                "created_at": datetime.now() - timedelta(days=2),
                "read": True
            }
        ]

        for notification in sample_notifications:
            self.create_notification_card(parent_frame, notification)

    def create_notification_card(self, parent, notification):
        """إنشاء بطاقة إشعار"""
        # تحديد لون الإطار حسب النوع والحالة
        if not notification["read"]:
            fg_color = ["#E3F2FD", "#1E3A8A"]  # أزرق فاتح للإشعارات غير المقروءة
        else:
            fg_color = ["#F5F5F5", "#2D2D2D"]  # رمادي للإشعارات المقروءة

        card_frame = ctk.CTkFrame(parent, fg_color=fg_color)
        card_frame.pack(fill="x", padx=10, pady=5)

        # الصف العلوي: الأيقونة والعنوان والوقت
        header_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        header_frame.pack(fill="x", padx=15, pady=(10, 5))

        # أيقونة حسب النوع
        icons = {
            "success": "✅",
            "warning": "⚠️",
            "error": "❌",
            "info": "ℹ️"
        }
        icon = icons.get(notification["type"], "📢")

        icon_label = ctk.CTkLabel(
            header_frame,
            text=icon,
            font=ctk.CTkFont(size=16)
        )
        icon_label.pack(side="left", padx=(0, 10))

        # العنوان
        title_label = ctk.CTkLabel(
            header_frame,
            text=notification["title"],
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(side="left")

        # الوقت
        time_ago = self.get_time_ago(notification["created_at"])
        time_label = ctk.CTkLabel(
            header_frame,
            text=time_ago,
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        time_label.pack(side="right")

        # الرسالة
        message_label = ctk.CTkLabel(
            card_frame,
            text=notification["message"],
            font=ctk.CTkFont(size=12),
            wraplength=500,
            justify="right"
        )
        message_label.pack(fill="x", padx=15, pady=(0, 10))

        # زر تمييز كمقروء (إذا لم يكن مقروءاً)
        if not notification["read"]:
            mark_read_button = ctk.CTkButton(
                card_frame,
                text="تمييز كمقروء",
                command=lambda: self.mark_notification_read(notification["id"], card_frame),
                width=100,
                height=25,
                font=ctk.CTkFont(size=10)
            )
            mark_read_button.pack(pady=(0, 10))

    def get_time_ago(self, created_at):
        """حساب الوقت المنقضي"""
        now = datetime.now()
        diff = now - created_at

        if diff.days > 0:
            return f"منذ {diff.days} يوم"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"منذ {hours} ساعة"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"منذ {minutes} دقيقة"
        else:
            return "الآن"

    def mark_notification_read(self, notification_id, card_frame):
        """تمييز إشعار كمقروء"""
        # تغيير لون الإطار
        card_frame.configure(fg_color=["#F5F5F5", "#2D2D2D"])

        # إخفاء زر التمييز
        for widget in card_frame.winfo_children():
            if isinstance(widget, ctk.CTkButton) and "تمييز كمقروء" in widget.cget("text"):
                widget.destroy()
                break

        # تحديث عداد الإشعارات
        self.update_notifications_count()

    def mark_all_notifications_read(self):
        """تمييز جميع الإشعارات كمقروءة"""
        messagebox.showinfo("تم", "تم تمييز جميع الإشعارات كمقروءة")
        self.update_notifications_count()

    def refresh_notifications_list(self, notifications_frame):
        """تحديث قائمة الإشعارات"""
        self.load_notifications(notifications_frame)

    def update_notifications_count(self):
        """تحديث عداد الإشعارات"""
        # في التطبيق الحقيقي، سيتم جلب العدد من قاعدة البيانات
        unread_count = 2  # مثال
        self.notifications_button.configure(text=f"🔔 الإشعارات ({unread_count})")

    def refresh_data(self):
        """تحديث البيانات"""
        def update():
            try:
                # تحديث عدد الإشعارات (مثال)
                notifications_count = 0
                self.root.after(0, lambda: self.notifications_button.configure(
                    text=f"🔔 الإشعارات ({notifications_count})"
                ))
            except Exception as e:
                print(f"خطأ في تحديث البيانات: {e}")
        
        threading.Thread(target=update, daemon=True).start()
    
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟")
        
        if result:
            self.auth_manager.logout()
            self.root.destroy()
            
            # فتح نافذة تسجيل الدخول
            login_window = LoginWindow(self.db_manager)
            login_window.run()
    
    def show(self):
        """عرض النافذة"""
        self.root.deiconify()
    
    def run(self):
        """تشغيل النافذة"""
        try:
            self.root.mainloop()
        except Exception as e:
            print(f"خطأ في تشغيل واجهة المدير: {e}")

# ===========================
# واجهة المحاسب
# ===========================

class AccountantWindow:
    def __init__(self, db_manager, auth_manager, user_data):
        """تهيئة واجهة المحاسب"""
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.user_data = user_data
        self.notification_manager = NotificationManager(db_manager)

        self.root = ctk.CTk()
        self.root.title(f"{Config.APP_NAME} - واجهة المحاسب")
        self.root.geometry(f"{Config.WINDOW_WIDTH}x{Config.WINDOW_HEIGHT}")

        self.center_window()
        self.create_widgets()
        self.refresh_data()

        # إضافة إشعار ترحيبي
        self.notification_manager.add_notification(
            "مرحباً بك",
            f"مرحباً {self.user_data['full_name']}، تم تسجيل دخولك بنجاح",
            "success",
            self.user_data.get('_id')
        )

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الشريط العلوي
        self.create_header()

        # الشريط الجانبي والمحتوى
        content_frame = ctk.CTkFrame(self.root)
        content_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        # الشريط الجانبي
        self.create_sidebar(content_frame)

        # المحتوى الرئيسي
        self.create_main_content(content_frame)

    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = ctk.CTkFrame(self.root, height=60)
        header_frame.pack(fill="x", padx=10, pady=10)
        header_frame.pack_propagate(False)

        # عنوان التطبيق
        title_label = ctk.CTkLabel(
            header_frame,
            text=f"مرحباً {self.user_data['full_name']} - واجهة المحاسب",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=15)

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        controls_frame.pack(side="right", padx=20, pady=10)

        # زر الإشعارات
        self.notifications_button = ctk.CTkButton(
            controls_frame,
            text="🔔 الإشعارات (0)",
            command=self.show_notifications,
            width=120,
            height=35
        )
        self.notifications_button.pack(side="right", padx=5)

        # زر تسجيل الخروج
        logout_button = ctk.CTkButton(
            controls_frame,
            text="تسجيل الخروج",
            command=self.logout,
            width=100,
            height=35,
            fg_color="red",
            hover_color="darkred"
        )
        logout_button.pack(side="right", padx=5)

    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        self.sidebar_frame = ctk.CTkFrame(parent, width=200)
        self.sidebar_frame.pack(side="left", fill="y", padx=(0, 10))
        self.sidebar_frame.pack_propagate(False)

        # عنوان القائمة
        menu_title = ctk.CTkLabel(
            self.sidebar_frame,
            text="القائمة الرئيسية",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        menu_title.pack(pady=(20, 10))

        # أزرار القائمة
        self.menu_buttons = {}

        menu_items = [
            ("📊 لوحة المعلومات", "dashboard"),
            ("👤 العملاء", "customers"),
            ("🧾 الفواتير", "invoices"),
            ("💰 المعاملات", "transactions"),
            ("📋 الحسابات", "accounts"),
            ("📈 التقارير", "reports"),
            ("📝 القيود اليومية", "journal_entries"),
            ("💳 المدفوعات", "payments"),
            ("📧 الرسائل", "messages")
        ]

        for text, key in menu_items:
            button = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                command=lambda k=key: self.show_section(k),
                width=180,
                height=40,
                anchor="w"
            )
            button.pack(pady=5, padx=10)
            self.menu_buttons[key] = button

        self.current_section = "dashboard"
        self.menu_buttons["dashboard"].configure(fg_color="gray")

    def create_main_content(self, parent):
        """إنشاء المحتوى الرئيسي"""
        self.main_frame = ctk.CTkFrame(parent)
        self.main_frame.pack(side="right", fill="both", expand=True)

        # لوحة المعلومات الافتراضية
        self.create_dashboard()

    def create_dashboard(self):
        """إنشاء لوحة المعلومات"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان اللوحة
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📊 لوحة معلومات المحاسب",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # الإحصائيات السريعة
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)

        stats_title = ctk.CTkLabel(
            stats_frame,
            text="الإحصائيات اليومية",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        stats_title.pack(pady=(15, 10))

        # بطاقات الإحصائيات
        cards_frame = ctk.CTkFrame(stats_frame)
        cards_frame.pack(fill="x", padx=20, pady=(0, 20))

        cards_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # بطاقة المعاملات اليوم
        self.create_stat_card(cards_frame, "💰", "معاملات اليوم", "0", 0, 0)

        # بطاقة إجمالي المبلغ
        self.create_stat_card(cards_frame, "💵", "إجمالي المبلغ", "0 ر.س", 0, 1)

        # بطاقة القيود المعلقة
        self.create_stat_card(cards_frame, "📝", "القيود المعلقة", "0", 0, 2)

        # بطاقة التقارير المطلوبة
        self.create_stat_card(cards_frame, "📈", "التقارير المطلوبة", "0", 0, 3)

        # الأنشطة الأخيرة
        activities_frame = ctk.CTkFrame(self.main_frame)
        activities_frame.pack(fill="both", expand=True, padx=20, pady=10)

        activities_title = ctk.CTkLabel(
            activities_frame,
            text="المعاملات الأخيرة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        activities_title.pack(pady=(15, 10))

        # قائمة المعاملات
        self.activities_list = ctk.CTkScrollableFrame(activities_frame, height=200)
        self.activities_list.pack(fill="both", expand=True, padx=20, pady=(0, 20))

    def create_stat_card(self, parent, icon, title, value, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")

        # الأيقونة
        icon_label = ctk.CTkLabel(
            card_frame,
            text=icon,
            font=ctk.CTkFont(size=30)
        )
        icon_label.pack(pady=(15, 5))

        # القيمة
        value_label = ctk.CTkLabel(
            card_frame,
            text=value,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        value_label.pack(pady=5)

        # العنوان
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=12)
        )
        title_label.pack(pady=(0, 15))

    def create_customers_view(self):
        """عرض العملاء للمحاسب"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="👤 عرض العملاء",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # جدول العملاء (للقراءة فقط)
        customers_frame = ctk.CTkFrame(self.main_frame)
        customers_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("كود العميل", "اسم العميل", "الشركة", "الهاتف", "البريد الإلكتروني", "الرصيد")

        customers_tree = ttk.Treeview(customers_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            customers_tree.heading(col, text=col)
            customers_tree.column(col, width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(customers_frame, orient="vertical", command=customers_tree.yview)
        customers_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        customers_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # تحميل بيانات العملاء
        try:
            customers = list(self.db_manager.customers_collection.find())

            for customer in customers:
                balance = f"{customer.get('balance', 0):,.2f} ر.س"

                customers_tree.insert("", "end", values=(
                    customer.get("customer_code", ""),
                    customer.get("customer_name", ""),
                    customer.get("company_name", ""),
                    customer.get("phone", ""),
                    customer.get("email", ""),
                    balance
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات العملاء: {str(e)}")

    def create_invoices_view(self):
        """عرض الفواتير للمحاسب"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="🧾 عرض الفواتير",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        new_invoice_button = ctk.CTkButton(
            controls_frame,
            text="➕ فاتورة جديدة",
            command=self.create_new_invoice_accountant,
            width=120,
            height=35
        )
        new_invoice_button.pack(side="left", padx=10, pady=10)

        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_invoices_accountant,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)

        # جدول الفواتير
        invoices_frame = ctk.CTkFrame(self.main_frame)
        invoices_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("رقم الفاتورة", "العميل", "التاريخ", "المبلغ الإجمالي", "الضريبة", "الصافي", "الحالة")

        self.invoices_tree_accountant = ttk.Treeview(invoices_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.invoices_tree_accountant.heading(col, text=col)
            self.invoices_tree_accountant.column(col, width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(invoices_frame, orient="vertical", command=self.invoices_tree_accountant.yview)
        self.invoices_tree_accountant.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.invoices_tree_accountant.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # تحديث قائمة الفواتير
        self.refresh_invoices_accountant()

    def create_new_invoice_accountant(self):
        """إنشاء فاتورة جديدة من واجهة المحاسب"""
        messagebox.showinfo("فاتورة جديدة", "سيتم فتح نافذة إنشاء فاتورة جديدة")

    def refresh_invoices_accountant(self):
        """تحديث قائمة الفواتير في واجهة المحاسب"""
        try:
            # مسح البيانات السابقة
            for item in self.invoices_tree_accountant.get_children():
                self.invoices_tree_accountant.delete(item)

            # جلب الفواتير من قاعدة البيانات
            invoices = list(self.db_manager.invoices_collection.find().sort("created_at", -1))

            for invoice in invoices:
                invoice_date = invoice.get("invoice_date", datetime.now()).strftime("%Y-%m-%d")

                # جلب اسم العميل
                customer = self.db_manager.customers_collection.find_one(
                    {"customer_code": invoice.get("customer_code")}
                )
                customer_name = customer.get("customer_name", "غير محدد") if customer else "غير محدد"

                self.invoices_tree_accountant.insert("", "end", values=(
                    invoice.get("invoice_number", ""),
                    customer_name,
                    invoice_date,
                    f"{invoice.get('subtotal', 0):,.2f}",
                    f"{invoice.get('tax_amount', 0):,.2f}",
                    f"{invoice.get('total_amount', 0):,.2f}",
                    invoice.get("status", "")
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث قائمة الفواتير: {str(e)}")

    def show_section(self, section_key):
        """عرض قسم معين"""
        # إعادة تعيين ألوان الأزرار
        for key, button in self.menu_buttons.items():
            if key == section_key:
                button.configure(fg_color="gray")
            else:
                button.configure(fg_color=["#3B8ED0", "#1F6AA5"])

        self.current_section = section_key

        # عرض المحتوى المناسب
        if section_key == "dashboard":
            self.create_dashboard()
        elif section_key == "customers":
            self.create_customers_view()
        elif section_key == "invoices":
            self.create_invoices_view()
        elif section_key == "transactions":
            self.create_transactions_section()
        elif section_key == "accounts":
            self.create_accounts_section()
        elif section_key == "reports":
            self.create_reports_section()
        elif section_key == "journal_entries":
            self.create_journal_entries_section()
        elif section_key == "payments":
            self.create_payments_section()
        elif section_key == "messages":
            self.create_messages_section()
        else:
            self.create_placeholder_section(section_key)

    def create_transactions_section(self):
        """إنشاء قسم المعاملات"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="💰 إدارة المعاملات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        add_transaction_button = ctk.CTkButton(
            controls_frame,
            text="➕ إضافة معاملة جديدة",
            command=self.add_transaction_dialog,
            width=150,
            height=35
        )
        add_transaction_button.pack(side="left", padx=10, pady=10)

        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_transactions_list,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)

        # جدول المعاملات
        transactions_frame = ctk.CTkFrame(self.main_frame)
        transactions_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("التاريخ", "النوع", "الوصف", "المبلغ", "الحساب", "الحالة")

        self.transactions_tree = ttk.Treeview(transactions_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.transactions_tree.heading(col, text=col)
            self.transactions_tree.column(col, width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(transactions_frame, orient="vertical", command=self.transactions_tree.yview)
        self.transactions_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.transactions_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # تحديث قائمة المعاملات
        self.refresh_transactions_list()

    def add_transaction_dialog(self):
        """نافذة إضافة معاملة جديدة"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إضافة معاملة جديدة")
        dialog.geometry("450x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="إضافة معاملة جديدة",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="x", padx=20, pady=10)

        # نوع المعاملة
        ctk.CTkLabel(fields_frame, text="نوع المعاملة:").pack(pady=(10, 5))
        transaction_type_var = ctk.StringVar(value="دخل")
        transaction_type_menu = ctk.CTkOptionMenu(
            fields_frame,
            values=["دخل", "مصروف", "تحويل"],
            variable=transaction_type_var,
            width=350
        )
        transaction_type_menu.pack(pady=(0, 10))

        # الوصف
        ctk.CTkLabel(fields_frame, text="وصف المعاملة:").pack(pady=(10, 5))
        description_entry = ctk.CTkEntry(fields_frame, width=350)
        description_entry.pack(pady=(0, 10))

        # المبلغ
        ctk.CTkLabel(fields_frame, text="المبلغ (ر.س):").pack(pady=(10, 5))
        amount_entry = ctk.CTkEntry(fields_frame, width=350)
        amount_entry.pack(pady=(0, 10))

        # الحساب
        ctk.CTkLabel(fields_frame, text="الحساب:").pack(pady=(10, 5))
        account_var = ctk.StringVar(value="النقدية")
        account_menu = ctk.CTkOptionMenu(
            fields_frame,
            values=["النقدية", "البنك", "العملاء", "الموردين", "المصروفات", "الإيرادات"],
            variable=account_var,
            width=350
        )
        account_menu.pack(pady=(0, 10))

        # التاريخ
        ctk.CTkLabel(fields_frame, text="التاريخ:").pack(pady=(10, 5))
        date_entry = ctk.CTkEntry(fields_frame, width=350)
        date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        date_entry.pack(pady=(0, 10))

        # ملاحظات
        ctk.CTkLabel(fields_frame, text="ملاحظات:").pack(pady=(10, 5))
        notes_textbox = ctk.CTkTextbox(fields_frame, width=350, height=80)
        notes_textbox.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        def save_transaction():
            # التحقق من البيانات
            transaction_type = transaction_type_var.get()
            description = description_entry.get().strip()
            amount_str = amount_entry.get().strip()
            account = account_var.get()
            date_str = date_entry.get().strip()
            notes = notes_textbox.get("1.0", "end-1c").strip()

            if not all([description, amount_str, date_str]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return

            try:
                amount = float(amount_str)
                transaction_date = datetime.strptime(date_str, "%Y-%m-%d")

                # إنشاء المعاملة
                transaction_data = {
                    "type": transaction_type,
                    "description": description,
                    "amount": amount,
                    "account": account,
                    "date": transaction_date,
                    "notes": notes,
                    "created_by": self.user_data["username"],
                    "created_at": self.db_manager.get_current_time(),
                    "status": "مكتملة"
                }

                self.db_manager.transactions_collection.insert_one(transaction_data)

                messagebox.showinfo("نجح", "تم إضافة المعاملة بنجاح")
                dialog.destroy()
                self.refresh_transactions_list()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة المعاملة: {str(e)}")

        save_button = ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_transaction,
            width=100,
            height=35
        )
        save_button.pack(side="left", padx=10, pady=10)

        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=10, pady=10)

    def refresh_transactions_list(self):
        """تحديث قائمة المعاملات"""
        try:
            # مسح البيانات السابقة
            for item in self.transactions_tree.get_children():
                self.transactions_tree.delete(item)

            # جلب المعاملات من قاعدة البيانات
            transactions = list(self.db_manager.transactions_collection.find().sort("created_at", -1).limit(50))

            for transaction in transactions:
                date_str = transaction.get("date", datetime.now()).strftime("%Y-%m-%d")
                amount_str = f"{transaction.get('amount', 0):.2f} ر.س"

                self.transactions_tree.insert("", "end", values=(
                    date_str,
                    transaction.get("type", ""),
                    transaction.get("description", ""),
                    amount_str,
                    transaction.get("account", ""),
                    transaction.get("status", "")
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث قائمة المعاملات: {str(e)}")

    def create_accounts_section(self):
        """إنشاء قسم الحسابات"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📋 إدارة الحسابات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # محتوى مؤقت
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قسم إدارة الحسابات قيد التطوير\nسيتم إضافة:\n• دليل الحسابات\n• أرصدة الحسابات\n• تقارير الحسابات",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(expand=True)

    def create_reports_section(self):
        """إنشاء قسم التقارير"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📈 التقارير المحاسبية",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # أنواع التقارير
        reports_frame = ctk.CTkFrame(self.main_frame)
        reports_frame.pack(fill="both", expand=True, padx=20, pady=10)

        reports_title = ctk.CTkLabel(
            reports_frame,
            text="التقارير المتاحة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        reports_title.pack(pady=(20, 20))

        # بطاقات التقارير
        reports_grid = ctk.CTkFrame(reports_frame)
        reports_grid.pack(fill="x", padx=20, pady=10)

        reports_grid.grid_columnconfigure((0, 1), weight=1)

        # تقرير المعاملات اليومية
        self.create_report_card(
            reports_grid,
            "📊 تقرير المعاملات اليومية",
            "عرض جميع المعاملات لليوم الحالي",
            lambda: self.generate_report("daily_transactions"),
            0, 0
        )

        # تقرير الأرصدة
        self.create_report_card(
            reports_grid,
            "💰 تقرير الأرصدة",
            "عرض أرصدة جميع الحسابات",
            lambda: self.generate_report("balances"),
            0, 1
        )

    def create_report_card(self, parent, title, description, command, row, col):
        """إنشاء بطاقة تقرير"""
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")

        # عنوان التقرير
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(pady=(15, 10))

        # وصف التقرير
        desc_label = ctk.CTkLabel(
            card_frame,
            text=description,
            font=ctk.CTkFont(size=11),
            wraplength=200
        )
        desc_label.pack(pady=(0, 15))

        # زر إنشاء التقرير
        generate_button = ctk.CTkButton(
            card_frame,
            text="إنشاء التقرير",
            command=command,
            width=150,
            height=35
        )
        generate_button.pack(pady=(0, 15))

    def generate_report(self, report_type):
        """إنشاء تقرير"""
        messagebox.showinfo("قريباً", f"سيتم إنشاء تقرير {report_type} قريباً")

    def create_journal_entries_section(self):
        """إنشاء قسم القيود اليومية"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📝 القيود اليومية",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # محتوى مؤقت
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قسم القيود اليومية قيد التطوير\nسيتم إضافة:\n• إنشاء قيود يومية\n• مراجعة القيود\n• ترحيل القيود",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(expand=True)

    def create_payments_section(self):
        """إنشاء قسم المدفوعات"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="💳 إدارة المدفوعات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # محتوى مؤقت
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قسم إدارة المدفوعات قيد التطوير\nسيتم إضافة:\n• تسجيل المدفوعات\n• متابعة المستحقات\n• تقارير المدفوعات",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(expand=True)

    def create_messages_section(self):
        """إنشاء قسم الرسائل"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📧 الرسائل والتواصل",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # محتوى مؤقت
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قسم الرسائل قيد التطوير\nسيتم إضافة:\n• إرسال رسائل للإدارة\n• استقبال التعليمات\n• الإشعارات",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(expand=True)

    def create_placeholder_section(self, section_name):
        """إنشاء قسم مؤقت"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # رسالة مؤقتة
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text=f"قسم {section_name} قيد التطوير",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        placeholder_label.pack(expand=True)

    def show_notifications(self):
        """عرض نافذة الإشعارات"""
        self.show_notifications_window()

    def show_notifications_window(self):
        """عرض نافذة الإشعارات المتقدمة"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("مركز الإشعارات")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="🔔 مركز الإشعارات",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(20, 10))

        # قائمة الإشعارات
        notifications_frame = ctk.CTkScrollableFrame(dialog, height=350)
        notifications_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # تحميل الإشعارات
        self.load_notifications(notifications_frame)

        # زر الإغلاق
        close_button = ctk.CTkButton(
            dialog,
            text="❌ إغلاق",
            command=dialog.destroy,
            width=100,
            height=35
        )
        close_button.pack(pady=20)

    def load_notifications(self, parent_frame):
        """تحميل الإشعارات"""
        # مسح الإشعارات السابقة
        for widget in parent_frame.winfo_children():
            widget.destroy()

        # إشعارات تجريبية للمحاسب
        sample_notifications = [
            {
                "id": "1",
                "title": "تذكير: مراجعة القيود",
                "message": "يرجى مراجعة القيود اليومية لهذا الأسبوع",
                "type": "warning",
                "created_at": datetime.now() - timedelta(hours=1),
                "read": False
            },
            {
                "id": "2",
                "title": "تقرير شهري جاهز",
                "message": "تم إنشاء التقرير المالي الشهري بنجاح",
                "type": "success",
                "created_at": datetime.now() - timedelta(hours=3),
                "read": False
            },
            {
                "id": "3",
                "title": "معاملة تحتاج موافقة",
                "message": "معاملة بقيمة 25,000 ر.س تحتاج موافقة المدير",
                "type": "info",
                "created_at": datetime.now() - timedelta(hours=6),
                "read": True
            }
        ]

        for notification in sample_notifications:
            self.create_notification_card(parent_frame, notification)

    def create_notification_card(self, parent, notification):
        """إنشاء بطاقة إشعار"""
        # تحديد لون الإطار حسب النوع والحالة
        if not notification["read"]:
            fg_color = ["#E3F2FD", "#1E3A8A"]  # أزرق فاتح للإشعارات غير المقروءة
        else:
            fg_color = ["#F5F5F5", "#2D2D2D"]  # رمادي للإشعارات المقروءة

        card_frame = ctk.CTkFrame(parent, fg_color=fg_color)
        card_frame.pack(fill="x", padx=10, pady=5)

        # الصف العلوي: الأيقونة والعنوان والوقت
        header_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        header_frame.pack(fill="x", padx=15, pady=(10, 5))

        # أيقونة حسب النوع
        icons = {
            "success": "✅",
            "warning": "⚠️",
            "error": "❌",
            "info": "ℹ️"
        }
        icon = icons.get(notification["type"], "📢")

        icon_label = ctk.CTkLabel(
            header_frame,
            text=icon,
            font=ctk.CTkFont(size=16)
        )
        icon_label.pack(side="left", padx=(0, 10))

        # العنوان
        title_label = ctk.CTkLabel(
            header_frame,
            text=notification["title"],
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(side="left")

        # الوقت
        time_ago = self.get_time_ago(notification["created_at"])
        time_label = ctk.CTkLabel(
            header_frame,
            text=time_ago,
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        time_label.pack(side="right")

        # الرسالة
        message_label = ctk.CTkLabel(
            card_frame,
            text=notification["message"],
            font=ctk.CTkFont(size=12),
            wraplength=500,
            justify="right"
        )
        message_label.pack(fill="x", padx=15, pady=(0, 10))

    def get_time_ago(self, created_at):
        """حساب الوقت المنقضي"""
        now = datetime.now()
        diff = now - created_at

        if diff.days > 0:
            return f"منذ {diff.days} يوم"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"منذ {hours} ساعة"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"منذ {minutes} دقيقة"
        else:
            return "الآن"

    def update_notifications_count(self):
        """تحديث عداد الإشعارات"""
        # في التطبيق الحقيقي، سيتم جلب العدد من قاعدة البيانات
        unread_count = 2  # مثال
        self.notifications_button.configure(text=f"🔔 الإشعارات ({unread_count})")

    def refresh_data(self):
        """تحديث البيانات"""
        def update():
            try:
                # تحديث عدد الإشعارات (مثال)
                notifications_count = 0
                self.root.after(0, lambda: self.notifications_button.configure(
                    text=f"🔔 الإشعارات ({notifications_count})"
                ))
            except Exception as e:
                print(f"خطأ في تحديث البيانات: {e}")

        threading.Thread(target=update, daemon=True).start()

    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟")

        if result:
            self.auth_manager.logout()
            self.root.destroy()

            login_window = LoginWindow(self.db_manager)
            login_window.run()

    def show(self):
        """عرض النافذة"""
        self.root.deiconify()

    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

# ===========================
# واجهة المورد
# ===========================

class SupplierWindow:
    def __init__(self, db_manager, auth_manager, user_data):
        """تهيئة واجهة المورد"""
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.user_data = user_data
        self.notification_manager = NotificationManager(db_manager)

        self.root = ctk.CTk()
        self.root.title(f"{Config.APP_NAME} - واجهة المورد")
        self.root.geometry(f"{Config.WINDOW_WIDTH}x{Config.WINDOW_HEIGHT}")

        self.center_window()
        self.create_widgets()
        self.refresh_data()

        # إضافة إشعار ترحيبي
        self.notification_manager.add_notification(
            "مرحباً بك",
            f"مرحباً {self.user_data['full_name']}، تم تسجيل دخولك بنجاح",
            "success",
            self.user_data.get('_id')
        )

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الشريط العلوي
        self.create_header()

        # الشريط الجانبي والمحتوى
        content_frame = ctk.CTkFrame(self.root)
        content_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        # الشريط الجانبي
        self.create_sidebar(content_frame)

        # المحتوى الرئيسي
        self.create_main_content(content_frame)

    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = ctk.CTkFrame(self.root, height=60)
        header_frame.pack(fill="x", padx=10, pady=10)
        header_frame.pack_propagate(False)

        # عنوان التطبيق
        title_label = ctk.CTkLabel(
            header_frame,
            text=f"مرحباً {self.user_data['full_name']} - واجهة المورد",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=15)

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        controls_frame.pack(side="right", padx=20, pady=10)

        # زر الإشعارات
        self.notifications_button = ctk.CTkButton(
            controls_frame,
            text="🔔 الإشعارات (0)",
            command=self.show_notifications,
            width=120,
            height=35
        )
        self.notifications_button.pack(side="right", padx=5)

        # زر تسجيل الخروج
        logout_button = ctk.CTkButton(
            controls_frame,
            text="تسجيل الخروج",
            command=self.logout,
            width=100,
            height=35,
            fg_color="red",
            hover_color="darkred"
        )
        logout_button.pack(side="right", padx=5)

    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        self.sidebar_frame = ctk.CTkFrame(parent, width=200)
        self.sidebar_frame.pack(side="left", fill="y", padx=(0, 10))
        self.sidebar_frame.pack_propagate(False)

        # عنوان القائمة
        menu_title = ctk.CTkLabel(
            self.sidebar_frame,
            text="القائمة الرئيسية",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        menu_title.pack(pady=(20, 10))

        # أزرار القائمة
        self.menu_buttons = {}

        menu_items = [
            ("📊 لوحة المعلومات", "dashboard"),
            ("🧾 الفواتير", "invoices"),
            ("💰 المستحقات", "receivables"),
            ("📦 طلبات التوريد", "supply_requests"),
            ("💳 المدفوعات", "payments"),
            ("📄 المستندات", "documents"),
            ("📧 الرسائل", "messages"),
            ("⚙️ الإعدادات", "settings")
        ]

        for text, key in menu_items:
            button = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                command=lambda k=key: self.show_section(k),
                width=180,
                height=40,
                anchor="w"
            )
            button.pack(pady=5, padx=10)
            self.menu_buttons[key] = button

        self.current_section = "dashboard"
        self.menu_buttons["dashboard"].configure(fg_color="gray")

    def create_main_content(self, parent):
        """إنشاء المحتوى الرئيسي"""
        self.main_frame = ctk.CTkFrame(parent)
        self.main_frame.pack(side="right", fill="both", expand=True)

        # لوحة المعلومات الافتراضية
        self.create_dashboard()

    def create_dashboard(self):
        """إنشاء لوحة المعلومات"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان اللوحة
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📊 لوحة معلومات المورد",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # الإحصائيات السريعة
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)

        stats_title = ctk.CTkLabel(
            stats_frame,
            text="الإحصائيات السريعة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        stats_title.pack(pady=(15, 10))

        # بطاقات الإحصائيات
        cards_frame = ctk.CTkFrame(stats_frame)
        cards_frame.pack(fill="x", padx=20, pady=(0, 20))

        cards_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # بطاقة الفواتير المعلقة
        self.create_stat_card(cards_frame, "🧾", "الفواتير المعلقة", "0", 0, 0)

        # بطاقة إجمالي المستحقات
        self.create_stat_card(cards_frame, "💰", "إجمالي المستحقات", "0 ر.س", 0, 1)

        # بطاقة طلبات التوريد
        self.create_stat_card(cards_frame, "📦", "طلبات التوريد", "0", 0, 2)

        # بطاقة المدفوعات المستلمة
        self.create_stat_card(cards_frame, "💳", "المدفوعات المستلمة", "0 ر.س", 0, 3)

        # الأنشطة الأخيرة
        activities_frame = ctk.CTkFrame(self.main_frame)
        activities_frame.pack(fill="both", expand=True, padx=20, pady=10)

        activities_title = ctk.CTkLabel(
            activities_frame,
            text="الأنشطة الأخيرة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        activities_title.pack(pady=(15, 10))

        # قائمة الأنشطة
        self.activities_list = ctk.CTkScrollableFrame(activities_frame, height=200)
        self.activities_list.pack(fill="both", expand=True, padx=20, pady=(0, 20))

    def create_stat_card(self, parent, icon, title, value, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")

        # الأيقونة
        icon_label = ctk.CTkLabel(
            card_frame,
            text=icon,
            font=ctk.CTkFont(size=30)
        )
        icon_label.pack(pady=(15, 5))

        # القيمة
        value_label = ctk.CTkLabel(
            card_frame,
            text=value,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        value_label.pack(pady=5)

        # العنوان
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=12)
        )
        title_label.pack(pady=(0, 15))

    def show_section(self, section_key):
        """عرض قسم معين"""
        # إعادة تعيين ألوان الأزرار
        for key, button in self.menu_buttons.items():
            if key == section_key:
                button.configure(fg_color="gray")
            else:
                button.configure(fg_color=["#3B8ED0", "#1F6AA5"])

        self.current_section = section_key

        # عرض المحتوى المناسب
        if section_key == "dashboard":
            self.create_dashboard()
        elif section_key == "invoices":
            self.create_invoices_section()
        elif section_key == "receivables":
            self.create_receivables_section()
        elif section_key == "supply_requests":
            self.create_supply_requests_section()
        elif section_key == "payments":
            self.create_payments_section()
        elif section_key == "documents":
            self.create_documents_section()
        elif section_key == "messages":
            self.create_messages_section()
        elif section_key == "settings":
            self.create_settings_section()
        else:
            self.create_placeholder_section(section_key)

    def create_invoices_section(self):
        """إنشاء قسم الفواتير"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="🧾 إدارة الفواتير",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        add_invoice_button = ctk.CTkButton(
            controls_frame,
            text="➕ إضافة فاتورة جديدة",
            command=self.add_invoice_dialog,
            width=150,
            height=35
        )
        add_invoice_button.pack(side="left", padx=10, pady=10)

        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_invoices_list,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)

        # جدول الفواتير
        invoices_frame = ctk.CTkFrame(self.main_frame)
        invoices_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("رقم الفاتورة", "التاريخ", "الوصف", "المبلغ", "الحالة", "تاريخ الاستحقاق")

        self.invoices_tree = ttk.Treeview(invoices_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.invoices_tree.heading(col, text=col)
            self.invoices_tree.column(col, width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(invoices_frame, orient="vertical", command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.invoices_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # تحديث قائمة الفواتير
        self.refresh_invoices_list()

    def add_invoice_dialog(self):
        """نافذة إضافة فاتورة جديدة"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إضافة فاتورة جديدة")
        dialog.geometry("450x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="إضافة فاتورة جديدة",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="x", padx=20, pady=10)

        # رقم الفاتورة
        ctk.CTkLabel(fields_frame, text="رقم الفاتورة:").pack(pady=(10, 5))
        invoice_number_entry = ctk.CTkEntry(fields_frame, width=350)
        invoice_number_entry.pack(pady=(0, 10))

        # وصف الفاتورة
        ctk.CTkLabel(fields_frame, text="وصف الفاتورة:").pack(pady=(10, 5))
        description_entry = ctk.CTkEntry(fields_frame, width=350)
        description_entry.pack(pady=(0, 10))

        # المبلغ
        ctk.CTkLabel(fields_frame, text="المبلغ (ر.س):").pack(pady=(10, 5))
        amount_entry = ctk.CTkEntry(fields_frame, width=350)
        amount_entry.pack(pady=(0, 10))

        # تاريخ الفاتورة
        ctk.CTkLabel(fields_frame, text="تاريخ الفاتورة:").pack(pady=(10, 5))
        invoice_date_entry = ctk.CTkEntry(fields_frame, width=350)
        invoice_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        invoice_date_entry.pack(pady=(0, 10))

        # تاريخ الاستحقاق
        ctk.CTkLabel(fields_frame, text="تاريخ الاستحقاق:").pack(pady=(10, 5))
        due_date_entry = ctk.CTkEntry(fields_frame, width=350)
        due_date_entry.insert(0, (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d"))
        due_date_entry.pack(pady=(0, 10))

        # ملاحظات
        ctk.CTkLabel(fields_frame, text="ملاحظات:").pack(pady=(10, 5))
        notes_textbox = ctk.CTkTextbox(fields_frame, width=350, height=80)
        notes_textbox.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        def save_invoice():
            # التحقق من البيانات
            invoice_number = invoice_number_entry.get().strip()
            description = description_entry.get().strip()
            amount_str = amount_entry.get().strip()
            invoice_date_str = invoice_date_entry.get().strip()
            due_date_str = due_date_entry.get().strip()
            notes = notes_textbox.get("1.0", "end-1c").strip()

            if not all([invoice_number, description, amount_str, invoice_date_str, due_date_str]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return

            try:
                amount = float(amount_str)
                invoice_date = datetime.strptime(invoice_date_str, "%Y-%m-%d")
                due_date = datetime.strptime(due_date_str, "%Y-%m-%d")

                # التحقق من عدم وجود رقم الفاتورة
                existing_invoice = self.db_manager.invoices_collection.find_one({"invoice_number": invoice_number})
                if existing_invoice:
                    messagebox.showerror("خطأ", "رقم الفاتورة موجود بالفعل")
                    return

                # إنشاء الفاتورة
                invoice_data = {
                    "invoice_number": invoice_number,
                    "description": description,
                    "amount": amount,
                    "invoice_date": invoice_date,
                    "due_date": due_date,
                    "notes": notes,
                    "supplier_id": self.user_data["username"],
                    "supplier_name": self.user_data["full_name"],
                    "created_at": self.db_manager.get_current_time(),
                    "status": "معلقة"
                }

                self.db_manager.invoices_collection.insert_one(invoice_data)

                messagebox.showinfo("نجح", "تم إضافة الفاتورة بنجاح")
                dialog.destroy()
                self.refresh_invoices_list()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح أو تاريخ صحيح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة الفاتورة: {str(e)}")

        save_button = ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_invoice,
            width=100,
            height=35
        )
        save_button.pack(side="left", padx=10, pady=10)

        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=10, pady=10)

    def refresh_invoices_list(self):
        """تحديث قائمة الفواتير"""
        try:
            # مسح البيانات السابقة
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)

            # جلب الفواتير من قاعدة البيانات للمورد الحالي
            invoices = list(self.db_manager.invoices_collection.find(
                {"supplier_id": self.user_data["username"]}
            ).sort("created_at", -1))

            for invoice in invoices:
                invoice_date_str = invoice.get("invoice_date", datetime.now()).strftime("%Y-%m-%d")
                due_date_str = invoice.get("due_date", datetime.now()).strftime("%Y-%m-%d")
                amount_str = f"{invoice.get('amount', 0):.2f} ر.س"

                self.invoices_tree.insert("", "end", values=(
                    invoice.get("invoice_number", ""),
                    invoice_date_str,
                    invoice.get("description", ""),
                    amount_str,
                    invoice.get("status", ""),
                    due_date_str
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث قائمة الفواتير: {str(e)}")

    def create_receivables_section(self):
        """إنشاء قسم المستحقات"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="💰 المستحقات والأرصدة",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # محتوى مؤقت
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قسم المستحقات قيد التطوير\nسيتم إضافة:\n• عرض الأرصدة المستحقة\n• تتبع المدفوعات\n• تقارير المستحقات",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(expand=True)

    def create_supply_requests_section(self):
        """إنشاء قسم طلبات التوريد"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📦 طلبات التوريد",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # محتوى مؤقت
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قسم طلبات التوريد قيد التطوير\nسيتم إضافة:\n• تقديم طلبات توريد جديدة\n• متابعة حالة الطلبات\n• تاريخ الطلبات",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(expand=True)

    def create_payments_section(self):
        """إنشاء قسم المدفوعات"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="💳 المدفوعات المستلمة",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # محتوى مؤقت
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قسم المدفوعات قيد التطوير\nسيتم إضافة:\n• عرض المدفوعات المستلمة\n• تأكيد استلام المدفوعات\n• تقارير المدفوعات",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(expand=True)

    def create_documents_section(self):
        """إنشاء قسم المستندات"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📄 إدارة المستندات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # محتوى مؤقت
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قسم المستندات قيد التطوير\nسيتم إضافة:\n• رفع المستندات والفواتير\n• إدارة الملفات\n• مشاركة المستندات",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(expand=True)

    def create_messages_section(self):
        """إنشاء قسم الرسائل"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📧 الرسائل والتواصل",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # محتوى مؤقت
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قسم الرسائل قيد التطوير\nسيتم إضافة:\n• إرسال رسائل للإدارة\n• استقبال الإشعارات\n• التواصل مع المحاسبين",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(expand=True)

    def create_settings_section(self):
        """إنشاء قسم الإعدادات"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="⚙️ إعدادات الحساب",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # محتوى مؤقت
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="قسم الإعدادات قيد التطوير\nسيتم إضافة:\n• تحديث بيانات الحساب\n• تغيير كلمة المرور\n• إعدادات الإشعارات",
            font=ctk.CTkFont(size=16)
        )
        placeholder_label.pack(expand=True)

    def create_placeholder_section(self, section_name):
        """إنشاء قسم مؤقت"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # رسالة مؤقتة
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text=f"قسم {section_name} قيد التطوير",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        placeholder_label.pack(expand=True)

    def show_notifications(self):
        """عرض نافذة الإشعارات"""
        self.show_notifications_window()

    def show_notifications_window(self):
        """عرض نافذة الإشعارات المتقدمة"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("مركز الإشعارات")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="🔔 مركز الإشعارات",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(20, 10))

        # قائمة الإشعارات
        notifications_frame = ctk.CTkScrollableFrame(dialog, height=350)
        notifications_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # تحميل الإشعارات
        self.load_notifications(notifications_frame)

        # زر الإغلاق
        close_button = ctk.CTkButton(
            dialog,
            text="❌ إغلاق",
            command=dialog.destroy,
            width=100,
            height=35
        )
        close_button.pack(pady=20)

    def load_notifications(self, parent_frame):
        """تحميل الإشعارات"""
        # مسح الإشعارات السابقة
        for widget in parent_frame.winfo_children():
            widget.destroy()

        # إشعارات تجريبية للمورد
        sample_notifications = [
            {
                "id": "1",
                "title": "طلب توريد جديد",
                "message": "تم استلام طلب توريد جديد بقيمة 50,000 ر.س",
                "type": "info",
                "created_at": datetime.now() - timedelta(minutes=30),
                "read": False
            },
            {
                "id": "2",
                "title": "دفعة مستحقة",
                "message": "دفعة بقيمة 15,000 ر.س مستحقة خلال 5 أيام",
                "type": "warning",
                "created_at": datetime.now() - timedelta(hours=2),
                "read": False
            },
            {
                "id": "3",
                "title": "تم الموافقة على الطلب",
                "message": "تم الموافقة على طلب التوريد رقم REQ-001",
                "type": "success",
                "created_at": datetime.now() - timedelta(hours=4),
                "read": True
            }
        ]

        for notification in sample_notifications:
            self.create_notification_card(parent_frame, notification)

    def create_notification_card(self, parent, notification):
        """إنشاء بطاقة إشعار"""
        # تحديد لون الإطار حسب النوع والحالة
        if not notification["read"]:
            fg_color = ["#E3F2FD", "#1E3A8A"]  # أزرق فاتح للإشعارات غير المقروءة
        else:
            fg_color = ["#F5F5F5", "#2D2D2D"]  # رمادي للإشعارات المقروءة

        card_frame = ctk.CTkFrame(parent, fg_color=fg_color)
        card_frame.pack(fill="x", padx=10, pady=5)

        # الصف العلوي: الأيقونة والعنوان والوقت
        header_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        header_frame.pack(fill="x", padx=15, pady=(10, 5))

        # أيقونة حسب النوع
        icons = {
            "success": "✅",
            "warning": "⚠️",
            "error": "❌",
            "info": "ℹ️"
        }
        icon = icons.get(notification["type"], "📢")

        icon_label = ctk.CTkLabel(
            header_frame,
            text=icon,
            font=ctk.CTkFont(size=16)
        )
        icon_label.pack(side="left", padx=(0, 10))

        # العنوان
        title_label = ctk.CTkLabel(
            header_frame,
            text=notification["title"],
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(side="left")

        # الوقت
        time_ago = self.get_time_ago(notification["created_at"])
        time_label = ctk.CTkLabel(
            header_frame,
            text=time_ago,
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        time_label.pack(side="right")

        # الرسالة
        message_label = ctk.CTkLabel(
            card_frame,
            text=notification["message"],
            font=ctk.CTkFont(size=12),
            wraplength=500,
            justify="right"
        )
        message_label.pack(fill="x", padx=15, pady=(0, 10))

    def get_time_ago(self, created_at):
        """حساب الوقت المنقضي"""
        now = datetime.now()
        diff = now - created_at

        if diff.days > 0:
            return f"منذ {diff.days} يوم"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"منذ {hours} ساعة"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"منذ {minutes} دقيقة"
        else:
            return "الآن"

    def update_notifications_count(self):
        """تحديث عداد الإشعارات"""
        # في التطبيق الحقيقي، سيتم جلب العدد من قاعدة البيانات
        unread_count = 2  # مثال
        self.notifications_button.configure(text=f"🔔 الإشعارات ({unread_count})")

    def refresh_data(self):
        """تحديث البيانات"""
        def update():
            try:
                # تحديث عدد الإشعارات (مثال)
                notifications_count = 0
                self.root.after(0, lambda: self.notifications_button.configure(
                    text=f"🔔 الإشعارات ({notifications_count})"
                ))
            except Exception as e:
                print(f"خطأ في تحديث البيانات: {e}")

        threading.Thread(target=update, daemon=True).start()

    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟")

        if result:
            self.auth_manager.logout()
            self.root.destroy()

            login_window = LoginWindow(self.db_manager)
            login_window.run()

    def show(self):
        """عرض النافذة"""
        self.root.deiconify()

    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

# ===========================
# التطبيق الرئيسي
# ===========================

class AccountingApp:
    def __init__(self):
        """تهيئة التطبيق الرئيسي"""
        # إعداد المظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # إنشاء المجلدات
        self.setup_directories()
        
        # تهيئة قاعدة البيانات
        self.db_manager = DatabaseManager()
        
        # إنشاء المستخدم الافتراضي
        self.create_default_admin()
        
        # بدء نافذة تسجيل الدخول
        self.login_window = LoginWindow(self.db_manager)
    
    def setup_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = ["data", "reports", "backups", "logs", "assets"]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def create_default_admin(self):
        """إنشاء حساب المدير الافتراضي"""
        try:
            admin_exists = self.db_manager.users_collection.find_one({"role": "admin"})
            
            if not admin_exists:
                salt = bcrypt.gensalt()
                hashed_password = bcrypt.hashpw("admin123".encode('utf-8'), salt).decode('utf-8')
                
                admin_user = {
                    "username": "admin",
                    "password": hashed_password,
                    "role": "admin",
                    "full_name": "مدير النظام",
                    "email": "<EMAIL>",
                    "phone": "",
                    "created_at": self.db_manager.get_current_time(),
                    "is_active": True
                }
                
                self.db_manager.users_collection.insert_one(admin_user)
                print("✅ تم إنشاء حساب المدير الافتراضي:")
                print("   اسم المستخدم: admin")
                print("   كلمة المرور: admin123")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء المدير الافتراضي: {e}")
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            print(f"🚀 بدء تشغيل {Config.APP_NAME} v{Config.APP_VERSION}")
            self.login_window.run()
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        except Exception as e:
            print(f"❌ خطأ في تشغيل التطبيق: {e}")
        finally:
            if hasattr(self, 'db_manager'):
                self.db_manager.close_connection()

# ===========================
# تشغيل التطبيق
# ===========================

def main():
    """الدالة الرئيسية"""
    try:
        app = AccountingApp()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في بدء التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()