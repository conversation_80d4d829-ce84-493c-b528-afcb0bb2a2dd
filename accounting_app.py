#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق محاسبي شامل - نظام إدارة محاسبي متكامل
المطور: فريق التطوير
الإصدار: 1.0.0

الوحدات الرئيسية:
1. واجهة المدير (Admin Page)
2. واجهة المحاسب (Accountant Page)  
3. واجهة المورد (Supplier Page)

المتطلبات:
- Python 3.8+
- MongoDB
- customtkinter
- pymongo
- bcrypt
- reportlab
- openpyxl
- pandas
- matplotlib
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk, filedialog
import pymongo
from pymongo import MongoClient
import bcrypt
from datetime import datetime, timezone, timedelta
import threading
import json
import os
from pathlib import Path
import logging
import secrets
import re

# ===========================
# إعدادات التطبيق
# ===========================

class Config:
    """إعدادات التطبيق"""
    APP_NAME = "نظام المحاسبة المتكامل"
    APP_VERSION = "1.0.0"
    
    # إعدادات قاعدة البيانات
    MONGODB_URI = "mongodb://localhost:27017/"
    DATABASE_NAME = "accounting_system"
    
    # إعدادات الواجهة
    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800
    
    # الألوان
    PRIMARY_COLOR = "#1f538d"
    SUCCESS_COLOR = "#28a745"
    ERROR_COLOR = "#dc3545"
    
    # العملة
    CURRENCY = "ر.س"

# ===========================
# مدير قاعدة البيانات
# ===========================

class DatabaseManager:
    def __init__(self):
        """تهيئة مدير قاعدة البيانات"""
        self.client = None
        self.db = None
        self.connect()
        self._setup_collections()
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.client = MongoClient(Config.MONGODB_URI, serverSelectionTimeoutMS=5000)
            self.client.server_info()
            self.db = self.client[Config.DATABASE_NAME]
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return True
        except Exception as e:
            print(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
            return False
    
    def _setup_collections(self):
        """إعداد المجموعات"""
        try:
            self.users_collection = self.db.users
            self.transactions_collection = self.db.transactions
            self.accounts_collection = self.db.accounts
            self.suppliers_collection = self.db.suppliers
            self.invoices_collection = self.db.invoices
            self.payments_collection = self.db.payments
            self.notifications_collection = self.db.notifications
            self.reports_collection = self.db.reports
            
            # إنشاء فهارس
            self.users_collection.create_index("username", unique=True)
            self.accounts_collection.create_index("account_code", unique=True)
            self.suppliers_collection.create_index("supplier_code", unique=True)
            self.invoices_collection.create_index("invoice_number", unique=True)
            
        except Exception as e:
            print(f"❌ خطأ في إعداد المجموعات: {e}")
    
    def get_current_time(self):
        """الحصول على الوقت الحالي"""
        return datetime.now(timezone.utc)
    
    def test_connection(self):
        """اختبار الاتصال"""
        try:
            self.client.server_info()
            return True
        except:
            return False
    
    def close_connection(self):
        """إغلاق الاتصال"""
        if self.client:
            self.client.close()

# ===========================
# نظام المصادقة
# ===========================

class AuthManager:
    def __init__(self, db_manager):
        """تهيئة مدير المصادقة"""
        self.db_manager = db_manager
        self.current_user = None
        self.session_token = None
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password, hashed_password):
        """التحقق من كلمة المرور"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
        except:
            return False
    
    def login(self, username, password):
        """تسجيل الدخول"""
        try:
            user = self.db_manager.users_collection.find_one({
                "username": username,
                "is_active": True
            })
            
            if not user:
                return None
            
            if not self.verify_password(password, user["password"]):
                return None
            
            self.current_user = user
            self.session_token = secrets.token_urlsafe(32)
            
            # تحديث آخر تسجيل دخول
            self.db_manager.users_collection.update_one(
                {"_id": user["_id"]},
                {"$set": {"last_login": self.db_manager.get_current_time()}}
            )
            
            return {
                "user_id": str(user["_id"]),
                "username": user["username"],
                "role": user["role"],
                "full_name": user["full_name"],
                "email": user["email"]
            }
            
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            return None
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None
        self.session_token = None
    
    def is_authenticated(self):
        """التحقق من صحة الجلسة"""
        return self.current_user is not None

# ===========================
# نافذة تسجيل الدخول
# ===========================

class LoginWindow:
    def __init__(self, db_manager):
        """تهيئة نافذة تسجيل الدخول"""
        self.db_manager = db_manager
        self.auth_manager = AuthManager(db_manager)
        
        self.root = ctk.CTk()
        self.root.title(f"{Config.APP_NAME} - تسجيل الدخول")
        self.root.geometry("400x500")
        self.root.resizable(False, False)
        
        self.center_window()
        self.create_widgets()
        
        self.login_attempts = 0
        self.max_attempts = 5
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # شعار التطبيق
        title_label = ctk.CTkLabel(
            main_frame,
            text=Config.APP_NAME,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(30, 10))
        
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="نظام إدارة محاسبي متكامل",
            font=ctk.CTkFont(size=14)
        )
        subtitle_label.pack(pady=(0, 30))
        
        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(main_frame)
        login_frame.pack(fill="x", padx=20, pady=20)
        
        login_title = ctk.CTkLabel(
            login_frame,
            text="تسجيل الدخول",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        login_title.pack(pady=(20, 10))
        
        # حقل اسم المستخدم
        username_label = ctk.CTkLabel(login_frame, text="اسم المستخدم:")
        username_label.pack(pady=(10, 5))
        
        self.username_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="أدخل اسم المستخدم",
            width=250,
            height=35
        )
        self.username_entry.pack(pady=(0, 10))
        
        # حقل كلمة المرور
        password_label = ctk.CTkLabel(login_frame, text="كلمة المرور:")
        password_label.pack(pady=(10, 5))
        
        self.password_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="أدخل كلمة المرور",
            show="*",
            width=250,
            height=35
        )
        self.password_entry.pack(pady=(0, 10))
        
        # زر تسجيل الدخول
        self.login_button = ctk.CTkButton(
            login_frame,
            text="تسجيل الدخول",
            command=self.login,
            width=250,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.login_button.pack(pady=(20, 10))
        
        # معلومات المدير الافتراضي
        info_label = ctk.CTkLabel(
            login_frame,
            text="المدير الافتراضي:\nاسم المستخدم: admin\nكلمة المرور: admin123",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        info_label.pack(pady=(10, 20))
        
        # حالة الاتصال
        self.connection_status = ctk.CTkLabel(
            main_frame,
            text="",
            font=ctk.CTkFont(size=10)
        )
        self.connection_status.pack(pady=(10, 0))
        
        self.check_database_connection()
        
        # ربط مفتاح Enter
        self.root.bind('<Return>', lambda event: self.login())
        self.username_entry.focus()
    
    def check_database_connection(self):
        """التحقق من حالة الاتصال"""
        def check():
            if self.db_manager.test_connection():
                self.connection_status.configure(
                    text="✅ متصل بقاعدة البيانات",
                    text_color="green"
                )
            else:
                self.connection_status.configure(
                    text="❌ فشل الاتصال بقاعدة البيانات",
                    text_color="red"
                )
        
        threading.Thread(target=check, daemon=True).start()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        if self.login_attempts >= self.max_attempts:
            messagebox.showerror("تم حظر الحساب", "تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول")
            return
        
        self.login_button.configure(state="disabled", text="جاري التحقق...")
        
        def perform_login():
            try:
                user_data = self.auth_manager.login(username, password)
                
                if user_data:
                    self.root.after(0, lambda: self.login_success(user_data))
                else:
                    self.login_attempts += 1
                    remaining = self.max_attempts - self.login_attempts
                    error_msg = f"اسم المستخدم أو كلمة المرور غير صحيحة\nالمحاولات المتبقية: {remaining}"
                    self.root.after(0, lambda: self.login_failed(error_msg))
                    
            except Exception as e:
                self.root.after(0, lambda: self.login_failed(f"خطأ في تسجيل الدخول: {str(e)}"))
        
        threading.Thread(target=perform_login, daemon=True).start()
    
    def login_success(self, user_data):
        """معالجة نجاح تسجيل الدخول"""
        self.login_button.configure(state="normal", text="تسجيل الدخول")
        self.root.withdraw()
        self.open_main_interface(user_data)
    
    def login_failed(self, error_message):
        """معالجة فشل تسجيل الدخول"""
        self.login_button.configure(state="normal", text="تسجيل الدخول")
        messagebox.showerror("فشل تسجيل الدخول", error_message)
        self.password_entry.delete(0, 'end')
        self.password_entry.focus()
    
    def open_main_interface(self, user_data):
        """فتح الواجهة المناسبة"""
        role = user_data["role"]
        
        try:
            if role == "admin":
                admin_window = AdminWindow(self.db_manager, self.auth_manager, user_data)
                admin_window.show()
            elif role == "accountant":
                accountant_window = AccountantWindow(self.db_manager, self.auth_manager, user_data)
                accountant_window.show()
            elif role == "supplier":
                supplier_window = SupplierWindow(self.db_manager, self.auth_manager, user_data)
                supplier_window.show()
            else:
                messagebox.showerror("خطأ", f"نوع المستخدم غير مدعوم: {role}")
                self.root.deiconify()
                return
            
            self.root.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح الواجهة: {str(e)}")
            self.root.deiconify()
    
    def run(self):
        """تشغيل النافذة"""
        try:
            self.root.mainloop()
        except Exception as e:
            print(f"خطأ في تشغيل النافذة: {e}")
        finally:
            if hasattr(self, 'root') and self.root.winfo_exists():
                self.root.destroy()

# ===========================
# واجهة المدير
# ===========================

class AdminWindow:
    def __init__(self, db_manager, auth_manager, user_data):
        """تهيئة واجهة المدير"""
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.user_data = user_data
        
        self.root = ctk.CTk()
        self.root.title(f"{Config.APP_NAME} - لوحة تحكم المدير")
        self.root.geometry(f"{Config.WINDOW_WIDTH}x{Config.WINDOW_HEIGHT}")
        
        self.center_window()
        self.create_widgets()
        self.refresh_data()
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الشريط العلوي
        self.create_header()
        
        # الشريط الجانبي والمحتوى
        content_frame = ctk.CTkFrame(self.root)
        content_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # الشريط الجانبي
        self.create_sidebar(content_frame)
        
        # المحتوى الرئيسي
        self.create_main_content(content_frame)
    
    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = ctk.CTkFrame(self.root, height=60)
        header_frame.pack(fill="x", padx=10, pady=10)
        header_frame.pack_propagate(False)
        
        # عنوان التطبيق
        title_label = ctk.CTkLabel(
            header_frame,
            text=f"مرحباً {self.user_data['full_name']} - لوحة تحكم المدير",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=15)
        
        # أزرار التحكم
        controls_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        controls_frame.pack(side="right", padx=20, pady=10)
        
        # زر الإشعارات
        self.notifications_button = ctk.CTkButton(
            controls_frame,
            text="🔔 الإشعارات (0)",
            command=self.show_notifications,
            width=120,
            height=35
        )
        self.notifications_button.pack(side="right", padx=5)
        
        # زر تسجيل الخروج
        logout_button = ctk.CTkButton(
            controls_frame,
            text="تسجيل الخروج",
            command=self.logout,
            width=100,
            height=35,
            fg_color="red",
            hover_color="darkred"
        )
        logout_button.pack(side="right", padx=5)
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        self.sidebar_frame = ctk.CTkFrame(parent, width=200)
        self.sidebar_frame.pack(side="left", fill="y", padx=(0, 10))
        self.sidebar_frame.pack_propagate(False)
        
        # عنوان القائمة
        menu_title = ctk.CTkLabel(
            self.sidebar_frame,
            text="القائمة الرئيسية",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        menu_title.pack(pady=(20, 10))
        
        # أزرار القائمة
        self.menu_buttons = {}
        
        menu_items = [
            ("📊 لوحة المعلومات", "dashboard"),
            ("👥 إدارة المستخدمين", "users"),
            ("📈 التقارير المالية", "reports"),
            ("💰 المعاملات", "transactions"),
            ("🏪 الموردين", "suppliers"),
            ("🧾 الفواتير", "invoices"),
            ("💳 المدفوعات", "payments"),
            ("🔧 إعدادات النظام", "settings")
        ]
        
        for text, key in menu_items:
            button = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                command=lambda k=key: self.show_section(k),
                width=180,
                height=40,
                anchor="w"
            )
            button.pack(pady=5, padx=10)
            self.menu_buttons[key] = button
        
        self.current_section = "dashboard"
        self.menu_buttons["dashboard"].configure(fg_color="gray")
    
    def create_main_content(self, parent):
        """إنشاء المحتوى الرئيسي"""
        self.main_frame = ctk.CTkFrame(parent)
        self.main_frame.pack(side="right", fill="both", expand=True)
        
        # لوحة المعلومات الافتراضية
        self.create_dashboard()
    
    def create_dashboard(self):
        """إنشاء لوحة المعلومات"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # عنوان اللوحة
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📊 لوحة المعلومات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # الإحصائيات السريعة
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="الإحصائيات السريعة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        stats_title.pack(pady=(15, 10))
        
        # بطاقات الإحصائيات
        cards_frame = ctk.CTkFrame(stats_frame)
        cards_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        cards_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # بطاقة المستخدمين
        self.create_stat_card(cards_frame, "👥", "إجمالي المستخدمين", "0", 0, 0)
        
        # بطاقة المعاملات
        self.create_stat_card(cards_frame, "💰", "إجمالي المعاملات", "0", 0, 1)
        
        # بطاقة الموردين
        self.create_stat_card(cards_frame, "🏪", "الموردين النشطين", "0", 0, 2)
        
        # بطاقة الفواتير
        self.create_stat_card(cards_frame, "🧾", "الفواتير المعلقة", "0", 0, 3)
        
        # الأنشطة الأخيرة
        activities_frame = ctk.CTkFrame(self.main_frame)
        activities_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        activities_title = ctk.CTkLabel(
            activities_frame,
            text="الأنشطة الأخيرة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        activities_title.pack(pady=(15, 10))
        
        # قائمة الأنشطة
        self.activities_list = ctk.CTkScrollableFrame(activities_frame, height=200)
        self.activities_list.pack(fill="both", expand=True, padx=20, pady=(0, 20))
    
    def create_stat_card(self, parent, icon, title, value, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # الأيقونة
        icon_label = ctk.CTkLabel(
            card_frame,
            text=icon,
            font=ctk.CTkFont(size=30)
        )
        icon_label.pack(pady=(15, 5))
        
        # القيمة
        value_label = ctk.CTkLabel(
            card_frame,
            text=value,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        value_label.pack(pady=5)
        
        # العنوان
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=12)
        )
        title_label.pack(pady=(0, 15))
    
    def show_section(self, section_key):
        """عرض قسم معين"""
        # إعادة تعيين ألوان الأزرار
        for key, button in self.menu_buttons.items():
            if key == section_key:
                button.configure(fg_color="gray")
            else:
                button.configure(fg_color=["#3B8ED0", "#1F6AA5"])
        
        self.current_section = section_key
        
        # عرض المحتوى المناسب
        if section_key == "dashboard":
            self.create_dashboard()
        elif section_key == "users":
            self.create_users_management()
        elif section_key == "reports":
            self.create_reports_section()
        else:
            self.create_placeholder_section(section_key)
    
    def create_users_management(self):
        """إنشاء قسم إدارة المستخدمين"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="👥 إدارة المستخدمين",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=20, pady=10)
        
        add_user_button = ctk.CTkButton(
            controls_frame,
            text="➕ إضافة مستخدم جديد",
            command=self.add_user_dialog,
            width=150,
            height=35
        )
        add_user_button.pack(side="left", padx=10, pady=10)
        
        refresh_button = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_users_list,
            width=100,
            height=35
        )
        refresh_button.pack(side="left", padx=10, pady=10)
        
        # جدول المستخدمين
        users_frame = ctk.CTkFrame(self.main_frame)
        users_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إنشاء Treeview للجدول
        columns = ("الاسم الكامل", "اسم المستخدم", "الدور", "البريد الإلكتروني", "الحالة", "تاريخ الإنشاء")
        
        self.users_tree = ttk.Treeview(users_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=150)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(users_frame, orient="vertical", command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        # تعبئة الجدول
        self.users_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)
        
        # تحديث قائمة المستخدمين
        self.refresh_users_list()
    
    def add_user_dialog(self):
        """نافذة إضافة مستخدم جديد"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("إضافة مستخدم جديد")
        dialog.geometry("400x500")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="إضافة مستخدم جديد",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # حقول الإدخال
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="x", padx=20, pady=10)
        
        # الاسم الكامل
        ctk.CTkLabel(fields_frame, text="الاسم الكامل:").pack(pady=(10, 5))
        full_name_entry = ctk.CTkEntry(fields_frame, width=300)
        full_name_entry.pack(pady=(0, 10))
        
        # اسم المستخدم
        ctk.CTkLabel(fields_frame, text="اسم المستخدم:").pack(pady=(10, 5))
        username_entry = ctk.CTkEntry(fields_frame, width=300)
        username_entry.pack(pady=(0, 10))
        
        # كلمة المرور
        ctk.CTkLabel(fields_frame, text="كلمة المرور:").pack(pady=(10, 5))
        password_entry = ctk.CTkEntry(fields_frame, width=300, show="*")
        password_entry.pack(pady=(0, 10))
        
        # البريد الإلكتروني
        ctk.CTkLabel(fields_frame, text="البريد الإلكتروني:").pack(pady=(10, 5))
        email_entry = ctk.CTkEntry(fields_frame, width=300)
        email_entry.pack(pady=(0, 10))
        
        # رقم الهاتف
        ctk.CTkLabel(fields_frame, text="رقم الهاتف:").pack(pady=(10, 5))
        phone_entry = ctk.CTkEntry(fields_frame, width=300)
        phone_entry.pack(pady=(0, 10))
        
        # الدور
        ctk.CTkLabel(fields_frame, text="الدور:").pack(pady=(10, 5))
        role_var = ctk.StringVar(value="accountant")
        role_menu = ctk.CTkOptionMenu(
            fields_frame,
            values=["admin", "accountant", "supplier"],
            variable=role_var,
            width=300
        )
        role_menu.pack(pady=(0, 20))
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)
        
        def save_user():
            # التحقق من البيانات
            full_name = full_name_entry.get().strip()
            username = username_entry.get().strip()
            password = password_entry.get().strip()
            email = email_entry.get().strip()
            phone = phone_entry.get().strip()
            role = role_var.get()
            
            if not all([full_name, username, password, email]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            try:
                # التحقق من عدم وجود اسم المستخدم
                existing_user = self.db_manager.users_collection.find_one({"username": username})
                if existing_user:
                    messagebox.showerror("خطأ", "اسم المستخدم موجود بالفعل")
                    return
                
                # تشفير كلمة المرور
                hashed_password = self.auth_manager.hash_password(password)
                
                # إنشاء المستخدم
                user_data = {
                    "username": username,
                    "password": hashed_password,
                    "role": role,
                    "full_name": full_name,
                    "email": email,
                    "phone": phone,
                    "created_at": self.db_manager.get_current_time(),
                    "is_active": True
                }
                
                self.db_manager.users_collection.insert_one(user_data)
                
                messagebox.showinfo("نجح", "تم إضافة المستخدم بنجاح")
                dialog.destroy()
                self.refresh_users_list()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة المستخدم: {str(e)}")
        
        save_button = ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=save_user,
            width=100,
            height=35
        )
        save_button.pack(side="left", padx=10, pady=10)
        
        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=10, pady=10)
    
    def refresh_users_list(self):
        """تحديث قائمة المستخدمين"""
        try:
            # مسح البيانات السابقة
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
            
            # جلب المستخدمين من قاعدة البيانات
            users = list(self.db_manager.users_collection.find())
            
            for user in users:
                status = "نشط" if user.get("is_active", True) else "غير نشط"
                created_date = user.get("created_at", datetime.now()).strftime("%Y-%m-%d")
                
                self.users_tree.insert("", "end", values=(
                    user.get("full_name", ""),
                    user.get("username", ""),
                    user.get("role", ""),
                    user.get("email", ""),
                    status,
                    created_date
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث قائمة المستخدمين: {str(e)}")
    
    def create_reports_section(self):
        """إنشاء قسم التقارير"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="📈 التقارير المالية",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # أنواع التقارير
        reports_frame = ctk.CTkFrame(self.main_frame)
        reports_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        reports_title = ctk.CTkLabel(
            reports_frame,
            text="أنواع التقارير المتاحة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        reports_title.pack(pady=(20, 20))
        
        # بطاقات التقارير
        reports_grid = ctk.CTkFrame(reports_frame)
        reports_grid.pack(fill="x", padx=20, pady=10)
        
        reports_grid.grid_columnconfigure((0, 1, 2), weight=1)
        
        # تقرير المعاملات الشهرية
        self.create_report_card(
            reports_grid, 
            "📊 تقرير المعاملات الشهرية",
            "عرض جميع المعاملات المالية للشهر الحالي",
            lambda: self.generate_report("monthly_transactions"),
            0, 0
        )
        
        # تقرير الموردين
        self.create_report_card(
            reports_grid,
            "🏪 تقرير الموردين",
            "عرض تفاصيل جميع الموردين ومعاملاتهم",
            lambda: self.generate_report("suppliers"),
            0, 1
        )
        
        # تقرير الفواتير
        self.create_report_card(
            reports_grid,
            "🧾 تقرير الفواتير",
            "عرض حالة جميع الفواتير والمدفوعات",
            lambda: self.generate_report("invoices"),
            0, 2
        )
    
    def create_report_card(self, parent, title, description, command, row, col):
        """إنشاء بطاقة تقرير"""
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # عنوان التقرير
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(pady=(15, 10))
        
        # وصف التقرير
        desc_label = ctk.CTkLabel(
            card_frame,
            text=description,
            font=ctk.CTkFont(size=11),
            wraplength=200
        )
        desc_label.pack(pady=(0, 15))
        
        # زر إنشاء التقرير
        generate_button = ctk.CTkButton(
            card_frame,
            text="إنشاء التقرير",
            command=command,
            width=150,
            height=35
        )
        generate_button.pack(pady=(0, 15))
    
    def generate_report(self, report_type):
        """إنشاء تقرير"""
        messagebox.showinfo("قريباً", f"سيتم إنشاء تقرير {report_type} قريباً")
    
    def create_placeholder_section(self, section_name):
        """إنشاء قسم مؤقت"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # رسالة مؤقتة
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text=f"قسم {section_name} قيد التطوير",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        placeholder_label.pack(expand=True)
    
    def show_notifications(self):
        """عرض الإشعارات"""
        messagebox.showinfo("الإشعارات", "لا توجد إشعارات جديدة")
    
    def refresh_data(self):
        """تحديث البيانات"""
        def update():
            try:
                # تحديث عدد الإشعارات (مثال)
                notifications_count = 0
                self.root.after(0, lambda: self.notifications_button.configure(
                    text=f"🔔 الإشعارات ({notifications_count})"
                ))
            except Exception as e:
                print(f"خطأ في تحديث البيانات: {e}")
        
        threading.Thread(target=update, daemon=True).start()
    
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟")
        
        if result:
            self.auth_manager.logout()
            self.root.destroy()
            
            # فتح نافذة تسجيل الدخول
            login_window = LoginWindow(self.db_manager)
            login_window.run()
    
    def show(self):
        """عرض النافذة"""
        self.root.deiconify()
    
    def run(self):
        """تشغيل النافذة"""
        try:
            self.root.mainloop()
        except Exception as e:
            print(f"خطأ في تشغيل واجهة المدير: {e}")

# ===========================
# واجهة المحاسب
# ===========================

class AccountantWindow:
    def __init__(self, db_manager, auth_manager, user_data):
        """تهيئة واجهة المحاسب"""
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.user_data = user_data
        
        self.root = ctk.CTk()
        self.root.title(f"{Config.APP_NAME} - واجهة المحاسب")
        self.root.geometry(f"{Config.WINDOW_WIDTH}x{Config.WINDOW_HEIGHT}")
        
        self.center_window()
        self.create_widgets()
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الشريط العلوي
        header_frame = ctk.CTkFrame(self.root, height=60)
        header_frame.pack(fill="x", padx=10, pady=10)
        header_frame.pack_propagate(False)
        
        title_label = ctk.CTkLabel(
            header_frame,
            text=f"مرحباً {self.user_data['full_name']} - واجهة المحاسب",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=15)
        
        logout_button = ctk.CTkButton(
            header_frame,
            text="تسجيل الخروج",
            command=self.logout,
            width=100,
            height=35,
            fg_color="red"
        )
        logout_button.pack(side="right", padx=20, pady=10)
        
        # المحتوى الرئيسي
        main_label = ctk.CTkLabel(
            self.root,
            text="🧾 واجهة المحاسب\n(قيد التطوير)",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        main_label.pack(expand=True)
    
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟")
        
        if result:
            self.auth_manager.logout()
            self.root.destroy()
            
            login_window = LoginWindow(self.db_manager)
            login_window.run()
    
    def show(self):
        """عرض النافذة"""
        self.root.deiconify()

# ===========================
# واجهة المورد
# ===========================

class SupplierWindow:
    def __init__(self, db_manager, auth_manager, user_data):
        """تهيئة واجهة المورد"""
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.user_data = user_data
        
        self.root = ctk.CTk()
        self.root.title(f"{Config.APP_NAME} - واجهة المورد")
        self.root.geometry(f"{Config.WINDOW_WIDTH}x{Config.WINDOW_HEIGHT}")
        
        self.center_window()
        self.create_widgets()
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الشريط العلوي
        header_frame = ctk.CTkFrame(self.root, height=60)
        header_frame.pack(fill="x", padx=10, pady=10)
        header_frame.pack_propagate(False)
        
        title_label = ctk.CTkLabel(
            header_frame,
            text=f"مرحباً {self.user_data['full_name']} - واجهة المورد",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=15)
        
        logout_button = ctk.CTkButton(
            header_frame,
            text="تسجيل الخروج",
            command=self.logout,
            width=100,
            height=35,
            fg_color="red"
        )
        logout_button.pack(side="right", padx=20, pady=10)
        
        # المحتوى الرئيسي
        main_label = ctk.CTkLabel(
            self.root,
            text="🚚 واجهة المورد\n(قيد التطوير)",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        main_label.pack(expand=True)
    
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟")
        
        if result:
            self.auth_manager.logout()
            self.root.destroy()
            
            login_window = LoginWindow(self.db_manager)
            login_window.run()
    
    def show(self):
        """عرض النافذة"""
        self.root.deiconify()

# ===========================
# التطبيق الرئيسي
# ===========================

class AccountingApp:
    def __init__(self):
        """تهيئة التطبيق الرئيسي"""
        # إعداد المظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # إنشاء المجلدات
        self.setup_directories()
        
        # تهيئة قاعدة البيانات
        self.db_manager = DatabaseManager()
        
        # إنشاء المستخدم الافتراضي
        self.create_default_admin()
        
        # بدء نافذة تسجيل الدخول
        self.login_window = LoginWindow(self.db_manager)
    
    def setup_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = ["data", "reports", "backups", "logs", "assets"]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def create_default_admin(self):
        """إنشاء حساب المدير الافتراضي"""
        try:
            admin_exists = self.db_manager.users_collection.find_one({"role": "admin"})
            
            if not admin_exists:
                salt = bcrypt.gensalt()
                hashed_password = bcrypt.hashpw("admin123".encode('utf-8'), salt).decode('utf-8')
                
                admin_user = {
                    "username": "admin",
                    "password": hashed_password,
                    "role": "admin",
                    "full_name": "مدير النظام",
                    "email": "<EMAIL>",
                    "phone": "",
                    "created_at": self.db_manager.get_current_time(),
                    "is_active": True
                }
                
                self.db_manager.users_collection.insert_one(admin_user)
                print("✅ تم إنشاء حساب المدير الافتراضي:")
                print("   اسم المستخدم: admin")
                print("   كلمة المرور: admin123")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء المدير الافتراضي: {e}")
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            print(f"🚀 بدء تشغيل {Config.APP_NAME} v{Config.APP_VERSION}")
            self.login_window.run()
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        except Exception as e:
            print(f"❌ خطأ في تشغيل التطبيق: {e}")
        finally:
            if hasattr(self, 'db_manager'):
                self.db_manager.close_connection()

# ===========================
# تشغيل التطبيق
# ===========================

def main():
    """الدالة الرئيسية"""
    try:
        app = AccountingApp()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في بدء التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()