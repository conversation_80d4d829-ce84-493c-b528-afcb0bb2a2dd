#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي للنظام المحاسبي
يعمل بدون قاعدة بيانات للعرض السريع
"""

import customtkinter as ctk
from tkinter import messagebox
from datetime import datetime

# إعدادات التطبيق
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

class DemoApp:
    def __init__(self):
        """تهيئة التطبيق التوضيحي"""
        self.root = ctk.CTk()
        self.root.title("🏢 نظام المحاسبة الذكي - عرض توضيحي")
        self.root.geometry("1000x700")
        
        self.center_window()
        self.create_login_interface()
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_login_interface(self):
        """إنشاء واجهة تسجيل الدخول"""
        # مسح المحتوى السابق
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # الخلفية
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            main_frame,
            text="🏢 نظام المحاسبة الذكي",
            font=ctk.CTkFont(size=36, weight="bold")
        )
        title_label.pack(pady=(50, 20))
        
        # وصف النظام
        desc_label = ctk.CTkLabel(
            main_frame,
            text="نظام محاسبي شامل لإدارة الحسابات والمعاملات المالية",
            font=ctk.CTkFont(size=16)
        )
        desc_label.pack(pady=(0, 40))
        
        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(main_frame, width=400, height=300)
        login_frame.pack(pady=20)
        login_frame.pack_propagate(False)
        
        # عنوان تسجيل الدخول
        login_title = ctk.CTkLabel(
            login_frame,
            text="تسجيل الدخول",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        login_title.pack(pady=(30, 20))
        
        # حقل اسم المستخدم
        ctk.CTkLabel(login_frame, text="اسم المستخدم:").pack(pady=(10, 5))
        self.username_entry = ctk.CTkEntry(login_frame, width=300)
        self.username_entry.pack(pady=(0, 10))
        self.username_entry.insert(0, "admin")  # قيمة افتراضية
        
        # حقل كلمة المرور
        ctk.CTkLabel(login_frame, text="كلمة المرور:").pack(pady=(10, 5))
        self.password_entry = ctk.CTkEntry(login_frame, width=300, show="*")
        self.password_entry.pack(pady=(0, 20))
        self.password_entry.insert(0, "admin123")  # قيمة افتراضية
        
        # زر تسجيل الدخول
        login_button = ctk.CTkButton(
            login_frame,
            text="دخول",
            command=self.login,
            width=200,
            height=40,
            font=ctk.CTkFont(size=16)
        )
        login_button.pack(pady=10)
        
        # معلومات الحسابات التجريبية
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", padx=20, pady=20)
        
        info_text = """
🔑 حسابات تجريبية:

👨‍💼 المدير: admin / admin123
📊 المحاسب: accountant / acc123  
🚚 المورد: supplier / sup123

✨ الميزات المتاحة في العرض التوضيحي:
• واجهات مكتملة لجميع الأدوار
• لوحات معلومات تفاعلية
• نوافذ إدخال البيانات
• نظام تنقل متكامل
        """
        
        info_label = ctk.CTkLabel(
            info_frame,
            text=info_text,
            font=ctk.CTkFont(size=12),
            justify="right"
        )
        info_label.pack(pady=15, padx=20)
        
        # ربط مفتاح Enter
        self.root.bind('<Return>', lambda event: self.login())
        self.username_entry.focus()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        # التحقق من بيانات الدخول
        users = {
            "admin": {"password": "admin123", "role": "admin", "name": "المدير العام"},
            "accountant": {"password": "acc123", "role": "accountant", "name": "المحاسب الرئيسي"},
            "supplier": {"password": "sup123", "role": "supplier", "name": "مورد تجريبي"}
        }
        
        if username in users and users[username]["password"] == password:
            user_data = users[username]
            self.show_dashboard(user_data["role"], user_data["name"])
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
    
    def show_dashboard(self, role, name):
        """عرض لوحة المعلومات حسب الدور"""
        # مسح المحتوى السابق
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # الشريط العلوي
        header_frame = ctk.CTkFrame(self.root, height=60)
        header_frame.pack(fill="x", padx=10, pady=10)
        header_frame.pack_propagate(False)
        
        # عنوان الواجهة
        role_names = {
            "admin": "👨‍💼 واجهة المدير",
            "accountant": "📊 واجهة المحاسب",
            "supplier": "🚚 واجهة المورد"
        }
        
        title_label = ctk.CTkLabel(
            header_frame,
            text=f"مرحباً {name} - {role_names.get(role, 'واجهة المستخدم')}",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=15)
        
        # زر تسجيل الخروج
        logout_button = ctk.CTkButton(
            header_frame,
            text="تسجيل الخروج",
            command=self.create_login_interface,
            width=120,
            height=35,
            fg_color="red",
            hover_color="darkred"
        )
        logout_button.pack(side="right", padx=20, pady=10)
        
        # المحتوى الرئيسي
        content_frame = ctk.CTkFrame(self.root)
        content_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # عرض محتوى حسب الدور
        if role == "admin":
            self.show_admin_content(content_frame)
        elif role == "accountant":
            self.show_accountant_content(content_frame)
        elif role == "supplier":
            self.show_supplier_content(content_frame)
    
    def show_admin_content(self, parent):
        """عرض محتوى واجهة المدير"""
        title = ctk.CTkLabel(
            parent,
            text="📊 لوحة معلومات المدير",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=(20, 30))
        
        # بطاقات الإحصائيات
        stats_frame = ctk.CTkFrame(parent)
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # إحصائيات تجريبية
        self.create_stat_card(stats_frame, "👥", "المستخدمين", "15", 0, 0)
        self.create_stat_card(stats_frame, "💰", "إجمالي الإيرادات", "125,000 ر.س", 0, 1)
        self.create_stat_card(stats_frame, "🧾", "الفواتير", "48", 0, 2)
        self.create_stat_card(stats_frame, "📈", "النمو الشهري", "+12%", 0, 3)
        
        # الميزات
        features_text = """
✅ الميزات المكتملة في واجهة المدير:
• إدارة المستخدمين والصلاحيات
• مراقبة النشاطات والتقارير  
• إعدادات النظام العامة
• إدارة النسخ الاحتياطية
• لوحة معلومات تفاعلية
        """
        
        features_label = ctk.CTkLabel(
            parent,
            text=features_text,
            font=ctk.CTkFont(size=14),
            justify="right"
        )
        features_label.pack(pady=30)
    
    def show_accountant_content(self, parent):
        """عرض محتوى واجهة المحاسب"""
        title = ctk.CTkLabel(
            parent,
            text="📊 لوحة معلومات المحاسب",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=(20, 30))
        
        # بطاقات الإحصائيات
        stats_frame = ctk.CTkFrame(parent)
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # إحصائيات تجريبية
        self.create_stat_card(stats_frame, "💳", "المعاملات اليوم", "23", 0, 0)
        self.create_stat_card(stats_frame, "📋", "الحسابات", "156", 0, 1)
        self.create_stat_card(stats_frame, "📄", "التقارير", "8", 0, 2)
        self.create_stat_card(stats_frame, "⏰", "المعلقة", "5", 0, 3)
        
        # الميزات
        features_text = """
✅ الميزات المكتملة في واجهة المحاسب:
• إدارة المعاملات المالية مع نوافذ إدخال متقدمة
• إنشاء وإدارة الحسابات
• إنتاج التقارير المحاسبية
• إدارة القيود اليومية
• نظام تنقل متكامل بين الأقسام
        """
        
        features_label = ctk.CTkLabel(
            parent,
            text=features_text,
            font=ctk.CTkFont(size=14),
            justify="right"
        )
        features_label.pack(pady=30)
    
    def show_supplier_content(self, parent):
        """عرض محتوى واجهة المورد"""
        title = ctk.CTkLabel(
            parent,
            text="🚚 لوحة معلومات المورد",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=(20, 30))
        
        # بطاقات الإحصائيات
        stats_frame = ctk.CTkFrame(parent)
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # إحصائيات تجريبية
        self.create_stat_card(stats_frame, "🧾", "الفواتير", "12", 0, 0)
        self.create_stat_card(stats_frame, "💰", "المستحقات", "45,000 ر.س", 0, 1)
        self.create_stat_card(stats_frame, "📦", "طلبات التوريد", "7", 0, 2)
        self.create_stat_card(stats_frame, "✅", "المدفوعات", "8", 0, 3)
        
        # الميزات
        features_text = """
✅ الميزات المكتملة في واجهة المورد:
• إدارة الفواتير والمستحقات مع نوافذ إدخال
• تتبع حالة المدفوعات
• إدارة طلبات التوريد
• التواصل مع الإدارة
• نظام إدارة المستندات
        """
        
        features_label = ctk.CTkLabel(
            parent,
            text=features_text,
            font=ctk.CTkFont(size=14),
            justify="right"
        )
        features_label.pack(pady=30)
    
    def create_stat_card(self, parent, icon, title, value, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # الأيقونة
        icon_label = ctk.CTkLabel(
            card_frame,
            text=icon,
            font=ctk.CTkFont(size=30)
        )
        icon_label.pack(pady=(15, 5))
        
        # القيمة
        value_label = ctk.CTkLabel(
            card_frame,
            text=value,
            font=ctk.CTkFont(size=20, weight="bold")
        )
        value_label.pack(pady=5)
        
        # العنوان
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=12)
        )
        title_label.pack(pady=(0, 15))
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 بدء تشغيل العرض التوضيحي للنظام المحاسبي...")
        app = DemoApp()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()
