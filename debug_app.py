#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشخيص وتشغيل نظام المحاسبة
"""

import sys
import traceback
import os

def debug_app():
    """تشخيص التطبيق"""
    print("🔍 بدء تشخيص نظام المحاسبة...")
    
    try:
        # التحقق من المكتبات
        print("\n📚 فحص المكتبات:")
        
        import customtkinter as ctk
        print(f"✅ CustomTkinter: {ctk.__version__}")
        
        import pymongo
        print(f"✅ PyMongo: {pymongo.__version__}")
        
        import bcrypt
        print(f"✅ bcrypt: {bcrypt.__version__}")
        
        import tkinter
        print(f"✅ Tkinter: متوفر")
        
        # التحقق من الملف
        print(f"\n📁 فحص الملف:")
        if os.path.exists('accounting_app.py'):
            print("✅ ملف accounting_app.py موجود")
            file_size = os.path.getsize('accounting_app.py')
            print(f"📏 حجم الملف: {file_size:,} بايت")
        else:
            print("❌ ملف accounting_app.py غير موجود")
            return False
        
        # استيراد التطبيق
        print(f"\n📱 استيراد التطبيق:")
        import accounting_app
        print("✅ تم استيراد accounting_app بنجاح")
        
        # إنشاء كائن التطبيق
        print(f"\n🏗️ إنشاء كائن التطبيق:")
        app = accounting_app.AccountingApp()
        print("✅ تم إنشاء كائن AccountingApp بنجاح")
        
        # تشغيل التطبيق
        print(f"\n🚀 تشغيل التطبيق:")
        print("📱 سيتم فتح نافذة التطبيق الآن...")
        app.run()
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد مكتبة: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        print(f"\n📋 تفاصيل الخطأ:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_app()
    if not success:
        print(f"\n⚠️ فشل في تشغيل التطبيق")
        input("اضغط Enter للخروج...")
    else:
        print(f"\n✅ تم تشغيل التطبيق بنجاح")
