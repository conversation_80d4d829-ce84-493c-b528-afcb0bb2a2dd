@echo off
echo ========================================
echo    نظام المحاسبة المتكامل
echo    تطبيق محاسبي شامل
echo ========================================
echo.

echo جاري التحقق من المتطلبات...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تحميل وتثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
echo.

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: pip غير متوفر
    pause
    exit /b 1
)

echo ✅ pip متوفر
echo.

REM تثبيت المتطلبات
echo جاري تثبيت المتطلبات...
pip install -r requirements.txt

if errorlevel 1 (
    echo خطأ في تثبيت المتطلبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo.

REM التحقق من MongoDB
echo جاري التحقق من MongoDB...
echo تأكد من تشغيل MongoDB على المنفذ 27017
echo.

REM تشغيل التطبيق
echo جاري تشغيل التطبيق...
echo.
python accounting_app.py

if errorlevel 1 (
    echo.
    echo خطأ في تشغيل التطبيق
    echo تأكد من:
    echo 1. تشغيل MongoDB
    echo 2. تثبيت جميع المتطلبات
    echo 3. صحة ملف التطبيق
)

echo.
pause